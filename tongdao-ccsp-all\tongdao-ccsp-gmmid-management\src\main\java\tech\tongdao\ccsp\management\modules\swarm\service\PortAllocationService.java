package tech.tongdao.ccsp.management.modules.swarm.service;

/**
 * 端口分配服务接口
 * 负责为容器动态分配和管理外部访问端口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface PortAllocationService {

    /**
     * 为容器分配一个可用的外部端口
     *
     * @param containerInstanceId 容器实例ID
     * @param userId 用户ID（可选，用于用户级别的端口分配）
     * @param preferredPort 首选端口（可选）
     * @return 分配的端口号，如果分配失败返回null
     */
    Integer allocatePort(Long containerInstanceId, Long userId, Integer preferredPort);

    /**
     * 为容器分配一个可用的外部端口（包含容器内部端口信息）
     *
     * @param containerInstanceId 容器实例ID
     * @param containerName 容器名称
     * @param servicePort 容器内部端口
     * @param userId 用户ID（可选，用于用户级别的端口分配）
     * @param username 用户名（可选）
     * @param preferredPort 首选端口（可选）
     * @return 分配的端口号，如果分配失败返回null
     */
    Integer allocatePortWithDetails(Long containerInstanceId, String containerName, Integer servicePort,
                                   Long userId, String username, Integer preferredPort);

    /**
     * 释放容器占用的端口
     *
     * @param containerInstanceId 容器实例ID
     * @return 是否释放成功
     */
    boolean releasePort(Long containerInstanceId);

    /**
     * 检查端口是否可用
     *
     * @param port 端口号
     * @return 是否可用
     */
    boolean isPortAvailable(Integer port);

    /**
     * 获取容器分配的端口
     *
     * @param containerInstanceId 容器实例ID
     * @return 分配的端口号，如果未分配返回null
     */
    Integer getContainerPort(Long containerInstanceId);

    /**
     * 获取用户分配的所有端口
     *
     * @param userId 用户ID
     * @return 端口列表
     */
    java.util.List<Integer> getUserPorts(Long userId);

    /**
     * 获取端口池使用统计
     *
     * @return 统计信息Map，包含总数、已用、可用等信息
     */
    java.util.Map<String, Object> getPortPoolStats();

    /**
     * 回收长时间未使用的端口
     *
     * @param unusedDays 未使用天数阈值
     * @return 回收的端口数量
     */
    int recycleUnusedPorts(int unusedDays);

    /**
     * 更新端口分配记录的容器ID
     *
     * @param oldContainerId 旧的容器ID
     * @param newContainerId 新的容器ID
     * @return 是否更新成功
     */
    boolean updateContainerId(Long oldContainerId, Long newContainerId);

    /**
     * 通过容器名称获取分配的端口
     *
     * @param containerName 容器名称
     * @return 分配的端口号，如果未找到返回null
     */
    Integer getPortByContainerName(String containerName);
}
