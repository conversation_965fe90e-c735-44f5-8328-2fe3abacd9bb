package tech.tongdao.ccsp.management.modules.swarm.form;

import tech.tongdao.ccsp.management.modules.swarm.entity.ContainerInstanceEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 容器实例表单对象
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class ContainerInstanceForm implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * Docker容器ID
     */
    private String containerId;

    /**
     * Docker服务ID
     */
    private String serviceId;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 使用的镜像ID
     */
    private Long imageId;

    /**
     * 镜像名称
     */
    private String imageName;

    /**
     * 镜像标签
     */
    private String imageTag;

    /**
     * 关联的应用ID
     */
    private Long applicationId;

    /**
     * 关联的应用名称
     */
    private String applicationName;

    /**
     * 部署节点ID
     */
    private Long nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 容器状态(running/stopped/failed/pending)
     */
    private String status;

    /**
     * 端口映射配置(JSON格式)
     */
    private String portMappings;

    /**
     * 环境变量(JSON格式)
     */
    private String environmentVars;

    /**
     * 卷挂载配置(JSON格式)
     */
    private String volumeMounts;

    /**
     * 资源限制配置(JSON格式)
     */
    private String resourceLimits;

    /**
     * 部署配置(JSON格式)
     */
    private String deployConfig;

    /**
     * 部署用户ID
     */
    private Long deployedBy;

    /**
     * 部署时间
     */
    private LocalDateTime deployTime;

    /**
     * 启动时间
     */
    private LocalDateTime startTime;

    /**
     * 停止时间
     */
    private LocalDateTime stopTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 聚合字段（通过关联查询或数据聚合获得）
    
    /**
     * 镜像完整名称（名称:标签）
     */
    private String imageFullName;
    
    /**
     * 节点IP地址
     */
    private String nodeIpAddress;
    
    /**
     * 部署用户名称
     */
    private String deployedByName;
    
    /**
     * 运行时间（秒）
     */
    private Long runningTime;
    
    /**
     * CPU使用率
     */
    private Double cpuUsage;
    
    /**
     * 内存使用率
     */
    private Double memoryUsage;


    


    // 分页查询参数
    private Integer page = 1;
    private Integer pageSize = 10;

    // 查询条件参数
    /**
     * 设备资源类型查询条件(VSM/CHSM/HSM)
     */
    private String deviceResourceType;

    public ContainerInstanceForm() {
    }

    public ContainerInstanceForm(ContainerInstanceEntity entity) {
        if (entity != null) {
            this.id = entity.getId();
            this.containerId = entity.getContainerId();
            this.serviceId = entity.getServiceId();
            this.containerName = entity.getContainerName();
            this.imageId = entity.getImageId();
            this.imageName = entity.getImageName();
            this.imageTag = entity.getImageTag();
            try {
                // 如果实体已有应用关联字段，进行赋值（向后兼容无字段场景）
                java.lang.reflect.Method getAppId = entity.getClass().getMethod("getApplicationId");
                Object appId = getAppId.invoke(entity);
                if (appId instanceof Long) {
                    this.applicationId = (Long) appId;
                }
            } catch (Exception ignore) {}
            try {
                java.lang.reflect.Method getAppName = entity.getClass().getMethod("getApplicationName");
                Object appName = getAppName.invoke(entity);
                if (appName instanceof String) {
                    this.applicationName = (String) appName;
                }
            } catch (Exception ignore) {}
            this.nodeId = entity.getNodeId();
            this.nodeName = entity.getNodeName();
            this.status = entity.getStatus();
            this.portMappings = entity.getPortMappings();
            this.environmentVars = entity.getEnvironmentVars();
            this.volumeMounts = entity.getVolumeMounts();
            this.resourceLimits = entity.getResourceLimits();
            this.deployConfig = entity.getDeployConfig();
            this.deployedBy = entity.getDeployedBy();
            this.deployTime = entity.getDeployTime();
            this.startTime = entity.getStartTime();
            this.stopTime = entity.getStopTime();
            this.createTime = entity.getCreateTime();
            this.updateTime = entity.getUpdateTime();
        }
    }

    /**
     * 转换为实体对象
     */
    public ContainerInstanceEntity toEntity() {
        ContainerInstanceEntity entity = new ContainerInstanceEntity();
        entity.setId(this.id);
        entity.setContainerId(this.containerId);
        entity.setServiceId(this.serviceId);
        entity.setContainerName(this.containerName);
        entity.setImageId(this.imageId);
        entity.setImageName(this.imageName);
        entity.setImageTag(this.imageTag);
        try {
            if (this.applicationId != null) {
                java.lang.reflect.Method setAppId = entity.getClass().getMethod("setApplicationId", Long.class);
                setAppId.invoke(entity, this.applicationId);
            }
        } catch (Exception ignore) {}
        try {
            if (this.applicationName != null) {
                java.lang.reflect.Method setAppName = entity.getClass().getMethod("setApplicationName", String.class);
                setAppName.invoke(entity, this.applicationName);
            }
        } catch (Exception ignore) {}
        entity.setNodeId(this.nodeId);
        entity.setNodeName(this.nodeName);
        entity.setStatus(this.status);
        entity.setPortMappings(this.portMappings);
        entity.setEnvironmentVars(this.environmentVars);
        entity.setVolumeMounts(this.volumeMounts);
        entity.setResourceLimits(this.resourceLimits);
        entity.setDeployConfig(this.deployConfig);
        entity.setDeployedBy(this.deployedBy);
        entity.setDeployTime(this.deployTime);
        entity.setStartTime(this.startTime);
        entity.setStopTime(this.stopTime);
        entity.setCreateTime(this.createTime);
        entity.setUpdateTime(this.updateTime);
        return entity;
    }

    /**
     * 从实体对象更新表单数据
     */
    public void updateFromEntity(ContainerInstanceEntity entity) {
        if (entity != null) {
            this.id = entity.getId();
            this.containerId = entity.getContainerId();
            this.serviceId = entity.getServiceId();
            this.containerName = entity.getContainerName();
            this.imageId = entity.getImageId();
            this.imageName = entity.getImageName();
            this.imageTag = entity.getImageTag();
            try {
                java.lang.reflect.Method getAppId = entity.getClass().getMethod("getApplicationId");
                Object appId = getAppId.invoke(entity);
                if (appId instanceof Long) {
                    this.applicationId = (Long) appId;
                }
            } catch (Exception ignore) {}
            try {
                java.lang.reflect.Method getAppName = entity.getClass().getMethod("getApplicationName");
                Object appName = getAppName.invoke(entity);
                if (appName instanceof String) {
                    this.applicationName = (String) appName;
                }
            } catch (Exception ignore) {}
            this.nodeId = entity.getNodeId();
            this.nodeName = entity.getNodeName();
            this.status = entity.getStatus();
            this.portMappings = entity.getPortMappings();
            this.environmentVars = entity.getEnvironmentVars();
            this.volumeMounts = entity.getVolumeMounts();
            this.resourceLimits = entity.getResourceLimits();
            this.deployConfig = entity.getDeployConfig();
            this.deployedBy = entity.getDeployedBy();
            this.deployTime = entity.getDeployTime();
            this.startTime = entity.getStartTime();
            this.stopTime = entity.getStopTime();
            this.createTime = entity.getCreateTime();
            this.updateTime = entity.getUpdateTime();
        }
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getContainerName() {
        return containerName;
    }

    public void setContainerName(String containerName) {
        this.containerName = containerName;
    }

    public Long getImageId() {
        return imageId;
    }

    public void setImageId(Long imageId) {
        this.imageId = imageId;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getImageTag() {
        return imageTag;
    }

    public void setImageTag(String imageTag) {
        this.imageTag = imageTag;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPortMappings() {
        return portMappings;
    }

    public void setPortMappings(String portMappings) {
        this.portMappings = portMappings;
    }

    public String getEnvironmentVars() {
        return environmentVars;
    }

    public void setEnvironmentVars(String environmentVars) {
        this.environmentVars = environmentVars;
    }

    public String getVolumeMounts() {
        return volumeMounts;
    }

    public void setVolumeMounts(String volumeMounts) {
        this.volumeMounts = volumeMounts;
    }

    public String getResourceLimits() {
        return resourceLimits;
    }

    public void setResourceLimits(String resourceLimits) {
        this.resourceLimits = resourceLimits;
    }

    public String getDeployConfig() {
        return deployConfig;
    }

    public void setDeployConfig(String deployConfig) {
        this.deployConfig = deployConfig;
    }

    public Long getDeployedBy() {
        return deployedBy;
    }

    public void setDeployedBy(Long deployedBy) {
        this.deployedBy = deployedBy;
    }

    public LocalDateTime getDeployTime() {
        return deployTime;
    }

    public void setDeployTime(LocalDateTime deployTime) {
        this.deployTime = deployTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getStopTime() {
        return stopTime;
    }

    public void setStopTime(LocalDateTime stopTime) {
        this.stopTime = stopTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getDeviceResourceType() {
        return deviceResourceType;
    }

    public void setDeviceResourceType(String deviceResourceType) {
        this.deviceResourceType = deviceResourceType;
    }

    // 聚合字段的getter和setter方法

    public String getImageFullName() {
        return imageFullName;
    }

    public void setImageFullName(String imageFullName) {
        this.imageFullName = imageFullName;
    }

    public String getNodeIpAddress() {
        return nodeIpAddress;
    }

    public void setNodeIpAddress(String nodeIpAddress) {
        this.nodeIpAddress = nodeIpAddress;
    }

    public String getDeployedByName() {
        return deployedByName;
    }

    public void setDeployedByName(String deployedByName) {
        this.deployedByName = deployedByName;
    }

    public Long getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(Long runningTime) {
        this.runningTime = runningTime;
    }

    public Double getCpuUsage() {
        return cpuUsage;
    }

    public void setCpuUsage(Double cpuUsage) {
        this.cpuUsage = cpuUsage;
    }

    public Double getMemoryUsage() {
        return memoryUsage;
    }

    public void setMemoryUsage(Double memoryUsage) {
        this.memoryUsage = memoryUsage;
    }





    /**
     * 验证表单数据是否有效
     */
    public boolean isValid() {
        // 检查必填字段
        if (containerName == null || containerName.trim().isEmpty()) {
            return false;
        }
        if (imageId == null) {
            return false;
        }
        if (imageName == null || imageName.trim().isEmpty()) {
            return false;
        }
        if (imageTag == null || imageTag.trim().isEmpty()) {
            return false;
        }
        if (status == null || status.trim().isEmpty()) {
            return false;
        }

        // 检查字段长度
        if (containerName.length() > 255) {
            return false;
        }
        if (imageName.length() > 255) {
            return false;
        }
        if (imageTag.length() > 100) {
            return false;
        }
        if (status.length() > 20) {
            return false;
        }

        return true;
    }

    /**
     * 获取验证错误信息
     */
    public String getValidationError() {
        if (containerName == null || containerName.trim().isEmpty()) {
            return "容器名称不能为空";
        }
        if (imageId == null) {
            return "镜像ID不能为空";
        }
        if (imageName == null || imageName.trim().isEmpty()) {
            return "镜像名称不能为空";
        }
        if (imageTag == null || imageTag.trim().isEmpty()) {
            return "镜像标签不能为空";
        }
        if (status == null || status.trim().isEmpty()) {
            return "容器状态不能为空";
        }
        if (containerName.length() > 255) {
            return "容器名称长度不能超过255个字符";
        }
        if (imageName.length() > 255) {
            return "镜像名称长度不能超过255个字符";
        }
        if (imageTag.length() > 100) {
            return "镜像标签长度不能超过100个字符";
        }
        if (status.length() > 20) {
            return "容器状态长度不能超过20个字符";
        }
        return null;
    }

    @Override
    public String toString() {
        return "ContainerInstanceForm{" +
                "id=" + id +
                ", containerId='" + containerId + '\'' +
                ", serviceId='" + serviceId + '\'' +
                ", containerName='" + containerName + '\'' +
                ", imageId=" + imageId +
                ", imageName='" + imageName + '\'' +
                ", imageTag='" + imageTag + '\'' +
                ", nodeId=" + nodeId +
                ", nodeName='" + nodeName + '\'' +
                ", status='" + status + '\'' +
                ", portMappings='" + portMappings + '\'' +
                ", environmentVars='" + environmentVars + '\'' +
                ", volumeMounts='" + volumeMounts + '\'' +
                ", resourceLimits='" + resourceLimits + '\'' +
                ", deployConfig='" + deployConfig + '\'' +
                ", deployedBy=" + deployedBy +
                ", deployTime=" + deployTime +
                ", startTime=" + startTime +
                ", stopTime=" + stopTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", page=" + page +
                ", pageSize=" + pageSize +
                '}';
    }
}