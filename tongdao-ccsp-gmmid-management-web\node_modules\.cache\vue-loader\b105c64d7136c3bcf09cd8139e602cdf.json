{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=template&id=32efffef&scoped=true", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1757058863660}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1729062152634}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"app-container\" },\n    [\n      _c(\"el-card\", { attrs: { shadow: \"never\" } }, [\n        _c(\n          \"div\",\n          { staticClass: \"filter-container clearfix\" },\n          [\n            _c(\n              \"el-form\",\n              {\n                ref: \"searchForm\",\n                attrs: { model: _vm.listQuery, inline: \"\" },\n                nativeOn: {\n                  submit: function ($event) {\n                    $event.preventDefault()\n                    return _vm.getList.apply(null, arguments)\n                  },\n                },\n              },\n              [\n                _c(\n                  \"div\",\n                  { staticClass: \"filter-inner\" },\n                  [\n                    _c(\n                      \"el-form-item\",\n                      { attrs: { label: \"容器名称\" } },\n                      [\n                        _c(\"el-input\", {\n                          attrs: { clearable: \"\", placeholder: \"容器名称\" },\n                          model: {\n                            value: _vm.listQuery.containerName,\n                            callback: function ($$v) {\n                              _vm.$set(\n                                _vm.listQuery,\n                                \"containerName\",\n                                typeof $$v === \"string\" ? $$v.trim() : $$v\n                              )\n                            },\n                            expression: \"listQuery.containerName\",\n                          },\n                        }),\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"el-form-item\",\n                      { attrs: { label: \"状态\" } },\n                      [\n                        _c(\n                          \"el-select\",\n                          {\n                            attrs: { clearable: \"\", placeholder: \"请选择状态\" },\n                            model: {\n                              value: _vm.listQuery.status,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.listQuery, \"status\", $$v)\n                              },\n                              expression: \"listQuery.status\",\n                            },\n                          },\n                          [\n                            _c(\"el-option\", {\n                              attrs: { label: \"运行中\", value: \"running\" },\n                            }),\n                            _c(\"el-option\", {\n                              attrs: { label: \"已停止\", value: \"stopped\" },\n                            }),\n                            _c(\"el-option\", {\n                              attrs: { label: \"已暂停\", value: \"paused\" },\n                            }),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"el-form-item\",\n                      { attrs: { label: \"所属应用\" } },\n                      [\n                        _c(\n                          \"el-select\",\n                          {\n                            attrs: {\n                              clearable: \"\",\n                              placeholder: \"请选择应用\",\n                              filterable: \"\",\n                            },\n                            model: {\n                              value: _vm.listQuery.applicationId,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.listQuery, \"applicationId\", $$v)\n                              },\n                              expression: \"listQuery.applicationId\",\n                            },\n                          },\n                          _vm._l(_vm.appOptions, function (app) {\n                            return _c(\"el-option\", {\n                              key: app.value,\n                              attrs: { label: app.label, value: app.value },\n                            })\n                          }),\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"el-form-item\",\n                      { attrs: { label: \"设备类型\" } },\n                      [\n                        _c(\n                          \"el-select\",\n                          {\n                            attrs: {\n                              clearable: \"\",\n                              placeholder: \"请选择设备类型\",\n                            },\n                            model: {\n                              value: _vm.listQuery.deviceResourceType,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.listQuery,\n                                  \"deviceResourceType\",\n                                  $$v\n                                )\n                              },\n                              expression: \"listQuery.deviceResourceType\",\n                            },\n                          },\n                          [\n                            _c(\"el-option\", {\n                              attrs: { label: \"VSM\", value: \"VSM\" },\n                            }),\n                            _c(\"el-option\", {\n                              attrs: { label: \"CHSM\", value: \"CHSM\" },\n                            }),\n                            _c(\"el-option\", {\n                              attrs: { label: \"HSM\", value: \"HSM\" },\n                            }),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"pull-right\" },\n                  [\n                    _c(\n                      \"el-button-group\",\n                      [\n                        _c(\n                          \"el-form-item\",\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"primary\" },\n                                on: { click: _vm.handleSearch },\n                              },\n                              [\n                                _c(\"i\", { staticClass: \"el-icon-search\" }),\n                                _vm._v(\" 查询\"),\n                              ]\n                            ),\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"info\" },\n                                on: { click: _vm.handleResetSearch },\n                              },\n                              [\n                                _c(\"i\", { staticClass: \"el-icon-refresh\" }),\n                                _vm._v(\" 重置\"),\n                              ]\n                            ),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ]\n            ),\n          ],\n          1\n        ),\n      ]),\n      _c(\n        \"el-card\",\n        { attrs: { shadow: \"never\" } },\n        [\n          _c(\"div\", { staticClass: \"filter-container clearfix\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"filter-inner\" },\n              [\n                _c(\n                  \"el-button\",\n                  {\n                    attrs: { type: \"primary\" },\n                    on: { click: _vm.handleDeployContainer },\n                  },\n                  [_vm._v(\"部署容器\")]\n                ),\n                _c(\n                  \"el-button\",\n                  {\n                    attrs: { type: \"success\" },\n                    on: { click: _vm.handleSyncStatus },\n                  },\n                  [_vm._v(\"同步状态\")]\n                ),\n              ],\n              1\n            ),\n          ]),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"dataTable\",\n              staticStyle: { width: \"100%\" },\n              attrs: { data: _vm.tableList },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"序号\",\n                  type: \"index\",\n                  width: \"50\",\n                },\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"容器名称\",\n                  \"min-width\": \"150\",\n                  prop: \"containerName\",\n                },\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"所属应用\",\n                  \"min-width\": \"140\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        _c(\"span\", [\n                          _vm._v(_vm._s(row.applicationName || \"-\")),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: { align: \"center\", label: \"镜像\", \"min-width\": \"150\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(row.imageName) +\n                              \":\" +\n                              _vm._s(row.imageTag || \"latest\")\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"状态\",\n                  \"min-width\": \"100\",\n                  prop: \"status\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        row.status === \"running\"\n                          ? _c(\"el-tag\", { attrs: { type: \"success\" } }, [\n                              _vm._v(\"运行中\"),\n                            ])\n                          : row.status === \"stopped\"\n                          ? _c(\"el-tag\", { attrs: { type: \"danger\" } }, [\n                              _vm._v(\"已停止\"),\n                            ])\n                          : row.status === \"paused\"\n                          ? _c(\"el-tag\", { attrs: { type: \"warning\" } }, [\n                              _vm._v(\"已暂停\"),\n                            ])\n                          : _c(\"el-tag\", [_vm._v(_vm._s(row.status))]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: { align: \"center\", label: \"端口\", \"min-width\": \"120\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        _c(\"span\", [\n                          _vm._v(_vm._s(_vm.formatPorts(row.portMappings))),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"设备资源\",\n                  \"min-width\": \"140\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        row.deviceResources && row.deviceResources.length > 0\n                          ? _c(\"div\", [\n                              row.deviceResources.length === 1\n                                ? _c(\n                                    \"div\",\n                                    { staticClass: \"device-resource-item\" },\n                                    [\n                                      _c(\n                                        \"el-tag\",\n                                        {\n                                          attrs: {\n                                            type: _vm.getDeviceTypeColor(\n                                              row.deviceResources[0]\n                                                .deviceResourceType\n                                            ),\n                                            size: \"mini\",\n                                          },\n                                        },\n                                        [\n                                          _vm._v(\n                                            \"\\n                \" +\n                                              _vm._s(\n                                                row.deviceResources[0]\n                                                  .deviceResourceType\n                                              ) +\n                                              \"\\n              \"\n                                          ),\n                                        ]\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"device-info-mini\" },\n                                        [\n                                          _vm._v(\n                                            \"\\n                \" +\n                                              _vm._s(\n                                                row.deviceResources[0]\n                                                  .deviceResourceName ||\n                                                  row.deviceResources[0]\n                                                    .deviceResourceId\n                                              ) +\n                                              \"\\n              \"\n                                          ),\n                                        ]\n                                      ),\n                                    ],\n                                    1\n                                  )\n                                : _c(\n                                    \"div\",\n                                    { staticClass: \"device-resource-compact\" },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"device-tags\" },\n                                        _vm._l(\n                                          row.deviceResources,\n                                          function (device) {\n                                            return _c(\n                                              \"el-tag\",\n                                              {\n                                                key: device.id,\n                                                staticStyle: { margin: \"1px\" },\n                                                attrs: {\n                                                  type: _vm.getDeviceTypeColor(\n                                                    device.deviceResourceType\n                                                  ),\n                                                  size: \"mini\",\n                                                  title:\n                                                    device.deviceResourceType +\n                                                    \": \" +\n                                                    (device.deviceResourceName ||\n                                                      device.deviceResourceId),\n                                                },\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \"\\n                  \" +\n                                                    _vm._s(\n                                                      device.deviceResourceType\n                                                    ) +\n                                                    \"\\n                \"\n                                                ),\n                                              ]\n                                            )\n                                          }\n                                        ),\n                                        1\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"device-count-info\" },\n                                        [\n                                          _vm._v(\n                                            \"\\n                共\" +\n                                              _vm._s(\n                                                row.deviceResources.length\n                                              ) +\n                                              \"个设备\\n              \"\n                                          ),\n                                        ]\n                                      ),\n                                    ]\n                                  ),\n                            ])\n                          : row.hsmDeviceId || row.hsmConfigured\n                          ? _c(\n                              \"div\",\n                              [\n                                _c(\n                                  \"el-tag\",\n                                  { attrs: { type: \"success\", size: \"mini\" } },\n                                  [_vm._v(\"HSM\")]\n                                ),\n                                row.hsmDeviceName\n                                  ? _c(\n                                      \"div\",\n                                      { staticClass: \"device-info-mini\" },\n                                      [\n                                        _vm._v(\n                                          \"\\n              \" +\n                                            _vm._s(row.hsmDeviceName) +\n                                            \"\\n            \"\n                                        ),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                              ],\n                              1\n                            )\n                          : _c(\"span\", [_vm._v(\"-\")]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"访问地址\",\n                  \"min-width\": \"180\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        row.accessUrl\n                          ? _c(\n                              \"div\",\n                              [\n                                _c(\n                                  \"el-link\",\n                                  {\n                                    attrs: {\n                                      href: row.accessUrl,\n                                      target: \"_blank\",\n                                      type: \"primary\",\n                                    },\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n              \" +\n                                        _vm._s(row.accessUrl) +\n                                        \"\\n            \"\n                                    ),\n                                  ]\n                                ),\n                                _c(\n                                  \"el-button\",\n                                  {\n                                    staticStyle: { \"margin-left\": \"5px\" },\n                                    attrs: { type: \"text\", size: \"mini\" },\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.copyToClipboard(\n                                          row.accessUrl\n                                        )\n                                      },\n                                    },\n                                  },\n                                  [\n                                    _c(\"i\", {\n                                      staticClass: \"el-icon-copy-document\",\n                                    }),\n                                  ]\n                                ),\n                              ],\n                              1\n                            )\n                          : row.status === \"running\"\n                          ? _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"text\", size: \"mini\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleConfigRoute(row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"\\n            配置访问\\n          \")]\n                            )\n                          : _c(\"span\", [_vm._v(\"-\")]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  label: \"部署信息\",\n                  \"min-width\": \"140\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        _c(\"div\", { staticClass: \"deploy-info\" }, [\n                          row.deployedByName\n                            ? _c(\"div\", { staticClass: \"deploy-user\" }, [\n                                _c(\"i\", { staticClass: \"el-icon-user\" }),\n                                _vm._v(\n                                  \" \" +\n                                    _vm._s(row.deployedByName) +\n                                    \"\\n            \"\n                                ),\n                              ])\n                            : _vm._e(),\n                          row.createTime\n                            ? _c(\"div\", { staticClass: \"deploy-time\" }, [\n                                _c(\"i\", { staticClass: \"el-icon-time\" }),\n                                _vm._v(\n                                  \" \" +\n                                    _vm._s(_vm.formatDate(row.createTime)) +\n                                    \"\\n            \"\n                                ),\n                              ])\n                            : _vm._e(),\n                          row.replicas && row.replicas > 1\n                            ? _c(\"div\", { staticClass: \"deploy-replicas\" }, [\n                                _c(\"i\", {\n                                  staticClass: \"el-icon-copy-document\",\n                                }),\n                                _vm._v(\n                                  \" 副本: \" +\n                                    _vm._s(row.replicas) +\n                                    \"\\n            \"\n                                ),\n                              ])\n                            : _vm._e(),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  align: \"center\",\n                  fixed: \"right\",\n                  label: \"操作\",\n                  width: \"250\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        row.status === \"running\"\n                          ? _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"text\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleStopContainer(row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"停止\")]\n                            )\n                          : _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"text\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleStartContainer(row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"启动\")]\n                            ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleRestartContainer(row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"重启\")]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleScaleContainer(row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"扩缩容\")]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleViewLogs(row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"日志\")]\n                        ),\n                        row.accessUrl\n                          ? _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"text\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleUpdateRoute(row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"更新路由\")]\n                            )\n                          : _vm._e(),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleRemoveContainer(row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"删除\")]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _c(\"pagination\", {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.total > _vm.listQuery.pageSize,\n                expression: \"total > listQuery.pageSize\",\n              },\n            ],\n            attrs: {\n              limit: _vm.listQuery.pageSize,\n              page: _vm.listQuery.page,\n              total: _vm.total,\n              layout: \"prev, pager, next\",\n            },\n            on: {\n              \"update:limit\": function ($event) {\n                return _vm.$set(_vm.listQuery, \"pageSize\", $event)\n              },\n              \"update:page\": function ($event) {\n                return _vm.$set(_vm.listQuery, \"page\", $event)\n              },\n              pagination: _vm.getList,\n            },\n          }),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            visible: _vm.deployDialogVisible,\n            title: \"部署容器\",\n            width: \"600px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.deployDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"deployForm\",\n              attrs: {\n                model: _vm.deployForm,\n                rules: _vm.deployRules,\n                \"label-width\": \"120px\",\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"关联应用\", prop: \"applicationId\" } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"100%\" },\n                      attrs: {\n                        filterable: \"\",\n                        placeholder: \"请选择应用\",\n                        loading: _vm.appOptionsLoading,\n                      },\n                      model: {\n                        value: _vm.deployForm.applicationId,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.deployForm, \"applicationId\", $$v)\n                        },\n                        expression: \"deployForm.applicationId\",\n                      },\n                    },\n                    _vm._l(_vm.appOptions, function (opt) {\n                      return _c(\"el-option\", {\n                        key: opt.value,\n                        attrs: { label: opt.label, value: String(opt.value) },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"容器名称\", prop: \"containerName\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"例如: my-nginx\" },\n                    model: {\n                      value: _vm.deployForm.containerName,\n                      callback: function ($$v) {\n                        _vm.$set(\n                          _vm.deployForm,\n                          \"containerName\",\n                          typeof $$v === \"string\" ? $$v.trim() : $$v\n                        )\n                      },\n                      expression: \"deployForm.containerName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"镜像选择\", prop: \"imageId\" } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"100%\" },\n                      attrs: {\n                        filterable: \"\",\n                        placeholder: \"请选择镜像\",\n                        loading: _vm.imageListLoading,\n                        disabled: _vm.imageList.length === 0,\n                      },\n                      on: { change: _vm.handleImageChange },\n                      model: {\n                        value: _vm.deployForm.imageId,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.deployForm, \"imageId\", $$v)\n                        },\n                        expression: \"deployForm.imageId\",\n                      },\n                    },\n                    [\n                      _vm._l(_vm.imageList, function (image) {\n                        return _c(\n                          \"el-option\",\n                          {\n                            key: image.id,\n                            attrs: {\n                              label: image.name + \":\" + image.tag,\n                              value: image.id,\n                            },\n                          },\n                          [\n                            _c(\"span\", { staticStyle: { float: \"left\" } }, [\n                              _vm._v(\n                                _vm._s(image.name) + \":\" + _vm._s(image.tag)\n                              ),\n                            ]),\n                            _c(\n                              \"span\",\n                              {\n                                staticStyle: {\n                                  float: \"right\",\n                                  color: \"#8492a6\",\n                                  \"font-size\": \"13px\",\n                                },\n                              },\n                              [\n                                _vm._v(\n                                  _vm._s(_vm.formatImageSize(image.sizeMb))\n                                ),\n                              ]\n                            ),\n                          ]\n                        )\n                      }),\n                      _vm.imageList.length === 0\n                        ? _c(\n                            \"div\",\n                            { attrs: { slot: \"empty\" }, slot: \"empty\" },\n                            [\n                              _vm.imageListLoading\n                                ? _c(\"span\", [_vm._v(\"加载中...\")])\n                                : _c(\"span\", [\n                                    _vm._v(\"暂无可用镜像，请先构建或导入镜像\"),\n                                  ]),\n                            ]\n                          )\n                        : _vm._e(),\n                    ],\n                    2\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"form-help\" },\n                    [\n                      _vm._v(\n                        \"\\n          选择已有的Docker镜像进行部署\\n          \"\n                      ),\n                      _c(\n                        \"el-button\",\n                        {\n                          staticStyle: { \"margin-left\": \"8px\" },\n                          attrs: { type: \"text\", size: \"mini\" },\n                          on: { click: _vm.loadImageList },\n                        },\n                        [\n                          _c(\"i\", { staticClass: \"el-icon-refresh\" }),\n                          _vm._v(\" 刷新\\n          \"),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm.selectedImage\n                ? _c(\"el-form-item\", { attrs: { label: \"镜像信息\" } }, [\n                    _c(\"div\", { staticClass: \"image-info\" }, [\n                      _c(\"p\", [\n                        _c(\"strong\", [_vm._v(\"名称：\")]),\n                        _vm._v(_vm._s(_vm.selectedImage.name)),\n                      ]),\n                      _c(\"p\", [\n                        _c(\"strong\", [_vm._v(\"标签：\")]),\n                        _vm._v(_vm._s(_vm.selectedImage.tag)),\n                      ]),\n                      _c(\"p\", [\n                        _c(\"strong\", [_vm._v(\"大小：\")]),\n                        _vm._v(\n                          _vm._s(_vm.formatImageSize(_vm.selectedImage.sizeMb))\n                        ),\n                      ]),\n                      _vm.selectedImage.description\n                        ? _c(\"p\", [\n                            _c(\"strong\", [_vm._v(\"描述：\")]),\n                            _vm._v(_vm._s(_vm.selectedImage.description)),\n                          ])\n                        : _vm._e(),\n                      _c(\"p\", [\n                        _c(\"strong\", [_vm._v(\"服务端口：\")]),\n                        _vm._v(\"8300（Console服务固定端口）\"),\n                      ]),\n                    ]),\n                  ])\n                : _vm._e(),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"副本数\", prop: \"replicas\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: { min: 1 },\n                    model: {\n                      value: _vm.deployForm.replicas,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.deployForm, \"replicas\", $$v)\n                      },\n                      expression: \"deployForm.replicas\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm.deployForm.replicas > 1\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"分布策略\" } },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"100%\" },\n                          attrs: { placeholder: \"请选择分布策略\" },\n                          model: {\n                            value: _vm.deployForm.distributionStrategy,\n                            callback: function ($$v) {\n                              _vm.$set(\n                                _vm.deployForm,\n                                \"distributionStrategy\",\n                                $$v\n                              )\n                            },\n                            expression: \"deployForm.distributionStrategy\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"跨节点分散（推荐）\",\n                              value: \"SPREAD_ACROSS_NODES\",\n                            },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"仅Worker节点\",\n                              value: \"WORKER_NODES_ONLY\",\n                            },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"仅Manager节点\",\n                              value: \"MANAGER_NODES_ONLY\",\n                            },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"平衡分布\", value: \"BALANCED\" },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"跨可用区分散\",\n                              value: \"SPREAD_ACROSS_ZONES\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\"div\", { staticClass: \"form-help\" }, [\n                        _vm._v(\"多副本时的部署分布策略，推荐选择跨节点分散\"),\n                      ]),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"设备资源配置\" } },\n                [\n                  _c(\"el-switch\", {\n                    attrs: { \"active-text\": \"启用\", \"inactive-text\": \"禁用\" },\n                    on: { change: _vm.handleDeviceResourceToggle },\n                    model: {\n                      value: _vm.deployForm.enableDeviceResource,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.deployForm, \"enableDeviceResource\", $$v)\n                      },\n                      expression: \"deployForm.enableDeviceResource\",\n                    },\n                  }),\n                  _c(\"div\", { staticClass: \"form-help\" }, [\n                    _vm._v(\"启用后可配置各类设备资源（HSM、VSM、CHSM等）\"),\n                  ]),\n                ],\n                1\n              ),\n              _vm.deployForm.enableDeviceResource\n                ? _c(\n                    \"el-form-item\",\n                    {\n                      attrs: { label: \"设备类型\", prop: \"deviceResourceType\" },\n                    },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"100%\" },\n                          attrs: { placeholder: \"请选择设备类型\" },\n                          on: { change: _vm.handleDeviceTypeChange },\n                          model: {\n                            value: _vm.deployForm.deviceResourceType,\n                            callback: function ($$v) {\n                              _vm.$set(\n                                _vm.deployForm,\n                                \"deviceResourceType\",\n                                $$v\n                              )\n                            },\n                            expression: \"deployForm.deviceResourceType\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: { label: \"VSM (虚拟密码机)\", value: \"VSM\" },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\"div\", { staticClass: \"form-help\" }, [\n                        _vm._v(\n                          \"选择要使用的设备资源类型，当前只支持VSM虚拟密码机\"\n                        ),\n                      ]),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.deployForm.enableDeviceResource &&\n              _vm.deployForm.deviceResourceType\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"分配类型\", prop: \"allocationType\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          model: {\n                            value: _vm.deployForm.allocationType,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.deployForm, \"allocationType\", $$v)\n                            },\n                            expression: \"deployForm.allocationType\",\n                          },\n                        },\n                        [\n                          _c(\"el-radio\", { attrs: { label: \"exclusive\" } }, [\n                            _vm._v(\"独占模式\"),\n                          ]),\n                          _c(\"el-radio\", { attrs: { label: \"shared\" } }, [\n                            _vm._v(\"共享模式\"),\n                          ]),\n                        ],\n                        1\n                      ),\n                      _c(\"div\", { staticClass: \"form-help\" }, [\n                        _vm._v(\n                          \"\\n          独占模式：设备只能被当前容器使用；共享模式：设备可被多个容器共享使用\\n        \"\n                        ),\n                      ]),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.deployForm.enableDeviceResource &&\n              _vm.deployForm.deviceResourceType\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"选择设备\", prop: \"deviceResourceIds\" } },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"100%\" },\n                          attrs: {\n                            multiple: \"\",\n                            filterable: \"\",\n                            placeholder:\n                              \"请选择\" +\n                              _vm.deployForm.deviceResourceType +\n                              \"设备\",\n                            loading: _vm.deviceResourceListLoading,\n                            disabled: _vm.availableDeviceResources.length === 0,\n                          },\n                          on: { change: _vm.handleDeviceResourceChange },\n                          model: {\n                            value: _vm.deployForm.deviceResourceIds,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.deployForm, \"deviceResourceIds\", $$v)\n                            },\n                            expression: \"deployForm.deviceResourceIds\",\n                          },\n                        },\n                        [\n                          _vm._l(\n                            _vm.availableDeviceResources,\n                            function (device) {\n                              return _c(\n                                \"el-option\",\n                                {\n                                  key: device.id,\n                                  attrs: {\n                                    label: device.name,\n                                    value: device.id,\n                                    disabled: !device.available,\n                                  },\n                                },\n                                [\n                                  _c(\"div\", { staticClass: \"device-option\" }, [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"device-option-main\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"device-name\" },\n                                          [_vm._v(_vm._s(device.name))]\n                                        ),\n                                        _c(\n                                          \"el-tag\",\n                                          {\n                                            staticClass: \"device-status\",\n                                            attrs: {\n                                              type: device.available\n                                                ? \"success\"\n                                                : \"danger\",\n                                              size: \"mini\",\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \"\\n                  \" +\n                                                _vm._s(\n                                                  device.available\n                                                    ? \"可用\"\n                                                    : \"不可用\"\n                                                ) +\n                                                \"\\n                \"\n                                            ),\n                                          ]\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"device-option-detail\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"device-address\" },\n                                          [\n                                            _vm._v(\n                                              _vm._s(device.ipAddress) +\n                                                \":\" +\n                                                _vm._s(device.port)\n                                            ),\n                                          ]\n                                        ),\n                                        device.totalCapacity\n                                          ? _c(\n                                              \"span\",\n                                              {\n                                                staticClass: \"device-capacity\",\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \"\\n                  容量: \" +\n                                                    _vm._s(\n                                                      device.usedCapacity || 0\n                                                    ) +\n                                                    \"/\" +\n                                                    _vm._s(\n                                                      device.totalCapacity\n                                                    ) +\n                                                    \"\\n                \"\n                                                ),\n                                              ]\n                                            )\n                                          : _vm._e(),\n                                      ]\n                                    ),\n                                  ]),\n                                ]\n                              )\n                            }\n                          ),\n                          _vm.availableDeviceResources.length === 0\n                            ? _c(\n                                \"div\",\n                                { attrs: { slot: \"empty\" }, slot: \"empty\" },\n                                [\n                                  _vm.deviceResourceListLoading\n                                    ? _c(\"span\", [_vm._v(\"加载中...\")])\n                                    : _c(\"span\", [\n                                        _vm._v(\n                                          \"暂无可用的\" +\n                                            _vm._s(\n                                              _vm.deployForm.deviceResourceType\n                                            ) +\n                                            \"设备\"\n                                        ),\n                                      ]),\n                                ]\n                              )\n                            : _vm._e(),\n                        ],\n                        2\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"form-help\" },\n                        [\n                          _vm._v(\n                            \"\\n          选择可用的\" +\n                              _vm._s(_vm.deployForm.deviceResourceType) +\n                              \"设备资源\\n          \"\n                          ),\n                          _c(\n                            \"el-button\",\n                            {\n                              staticStyle: { \"margin-left\": \"8px\" },\n                              attrs: { type: \"text\", size: \"mini\" },\n                              on: { click: _vm.loadDeviceResourceList },\n                            },\n                            [\n                              _c(\"i\", { staticClass: \"el-icon-refresh\" }),\n                              _vm._v(\" 刷新\\n          \"),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.deployForm.enableDeviceResource &&\n              _vm.selectedDeviceResources.length > 0\n                ? _c(\"el-form-item\", { attrs: { label: \"设备信息\" } }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"selected-devices\" },\n                      _vm._l(_vm.selectedDeviceResources, function (device) {\n                        return _c(\n                          \"div\",\n                          { key: device.id, staticClass: \"device-info-card\" },\n                          [\n                            _c(\n                              \"div\",\n                              { staticClass: \"device-card-header\" },\n                              [\n                                _c(\n                                  \"span\",\n                                  { staticClass: \"device-card-name\" },\n                                  [_vm._v(_vm._s(device.name))]\n                                ),\n                                _c(\n                                  \"el-tag\",\n                                  {\n                                    attrs: {\n                                      type: _vm.getDeviceTypeColor(device.type),\n                                      size: \"mini\",\n                                    },\n                                  },\n                                  [_vm._v(_vm._s(device.type))]\n                                ),\n                              ],\n                              1\n                            ),\n                            _c(\"div\", { staticClass: \"device-card-content\" }, [\n                              _c(\"p\", [\n                                _c(\"strong\", [_vm._v(\"地址：\")]),\n                                _vm._v(\n                                  _vm._s(device.ipAddress) +\n                                    \":\" +\n                                    _vm._s(device.port)\n                                ),\n                              ]),\n                              device.managementPort\n                                ? _c(\"p\", [\n                                    _c(\"strong\", [_vm._v(\"管理端口：\")]),\n                                    _vm._v(_vm._s(device.managementPort)),\n                                  ])\n                                : _vm._e(),\n                              _c(\n                                \"p\",\n                                [\n                                  _c(\"strong\", [_vm._v(\"状态：\")]),\n                                  device.status === \"normal\"\n                                    ? _c(\n                                        \"el-tag\",\n                                        {\n                                          attrs: {\n                                            type: \"success\",\n                                            size: \"mini\",\n                                          },\n                                        },\n                                        [_vm._v(\"正常\")]\n                                      )\n                                    : device.status === \"running\"\n                                    ? _c(\n                                        \"el-tag\",\n                                        {\n                                          attrs: {\n                                            type: \"success\",\n                                            size: \"mini\",\n                                          },\n                                        },\n                                        [_vm._v(\"运行中\")]\n                                      )\n                                    : _c(\n                                        \"el-tag\",\n                                        {\n                                          attrs: {\n                                            type: \"warning\",\n                                            size: \"mini\",\n                                          },\n                                        },\n                                        [_vm._v(_vm._s(device.status))]\n                                      ),\n                                ],\n                                1\n                              ),\n                              device.totalCapacity\n                                ? _c(\"p\", [\n                                    _c(\"strong\", [_vm._v(\"容量：\")]),\n                                    _vm._v(\n                                      _vm._s(device.usedCapacity || 0) +\n                                        \"/\" +\n                                        _vm._s(device.totalCapacity)\n                                    ),\n                                  ])\n                                : _vm._e(),\n                              device.description\n                                ? _c(\"p\", [\n                                    _c(\"strong\", [_vm._v(\"描述：\")]),\n                                    _vm._v(_vm._s(device.description)),\n                                  ])\n                                : _vm._e(),\n                            ]),\n                          ]\n                        )\n                      }),\n                      0\n                    ),\n                  ])\n                : _vm._e(),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.deployDialogVisible = false\n                    },\n                  },\n                },\n                [_vm._v(\"取消\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: { click: _vm.confirmDeployContainer },\n                },\n                [_vm._v(\"部署\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            visible: _vm.logsDialogVisible,\n            title: \"容器日志\",\n            width: \"800px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.logsDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\"el-input\", {\n            attrs: { rows: 20, readonly: \"\", type: \"textarea\" },\n            model: {\n              value: _vm.containerLogs,\n              callback: function ($$v) {\n                _vm.containerLogs = $$v\n              },\n              expression: \"containerLogs\",\n            },\n          }),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.logsDialogVisible = false\n                    },\n                  },\n                },\n                [_vm._v(\"关闭\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            visible: _vm.scaleDialogVisible,\n            title: \"容器扩缩容\",\n            width: \"500px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.scaleDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"scaleForm\",\n              attrs: {\n                model: _vm.scaleForm,\n                rules: _vm.scaleRules,\n                \"label-width\": \"100px\",\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"副本数\", prop: \"replicas\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: { min: 1 },\n                    model: {\n                      value: _vm.scaleForm.replicas,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.scaleForm, \"replicas\", $$v)\n                      },\n                      expression: \"scaleForm.replicas\",\n                    },\n                  }),\n                  _c(\"div\", { staticClass: \"form-help\" }, [\n                    _vm._v(\"当前运行的容器实例数量\"),\n                  ]),\n                ],\n                1\n              ),\n              _vm.scaleForm.replicas > 1\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"分布策略\" } },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"100%\" },\n                          attrs: { placeholder: \"请选择分布策略\" },\n                          model: {\n                            value: _vm.scaleForm.distributionStrategy,\n                            callback: function ($$v) {\n                              _vm.$set(\n                                _vm.scaleForm,\n                                \"distributionStrategy\",\n                                $$v\n                              )\n                            },\n                            expression: \"scaleForm.distributionStrategy\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"跨节点分散（推荐）\",\n                              value: \"SPREAD_ACROSS_NODES\",\n                            },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"仅Worker节点\",\n                              value: \"WORKER_NODES_ONLY\",\n                            },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: {\n                              label: \"仅Manager节点\",\n                              value: \"MANAGER_NODES_ONLY\",\n                            },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"平衡分布\", value: \"BALANCED\" },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\"div\", { staticClass: \"form-help\" }, [\n                        _vm._v(\"多副本时的部署分布策略\"),\n                      ]),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.scaleDialogVisible = false\n                    },\n                  },\n                },\n                [_vm._v(\"取消\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: { click: _vm.confirmScaleContainer },\n                },\n                [_vm._v(\"确认\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}