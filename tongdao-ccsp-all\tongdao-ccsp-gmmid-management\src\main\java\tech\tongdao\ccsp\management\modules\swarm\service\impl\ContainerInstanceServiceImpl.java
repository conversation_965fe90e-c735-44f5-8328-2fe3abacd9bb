package tech.tongdao.ccsp.management.modules.swarm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccsp.gmmid.beans.exceptions.BusinessException;
import com.ccsp.gmmid.beans.form.Pager;
import com.github.dockerjava.api.model.PortConfig;
import com.github.dockerjava.api.model.PortConfigProtocol;
import com.github.dockerjava.api.model.Service;
import com.github.dockerjava.api.model.Task;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;
import tech.tongdao.ccsp.management.modules.instance.entity.ApplicationEntity;
import tech.tongdao.ccsp.management.modules.instance.service.ApplicationService;
import tech.tongdao.ccsp.management.modules.swarm.converter.SwarmEntityMapper;
import tech.tongdao.ccsp.management.modules.swarm.entity.ContainerInstanceEntity;
import tech.tongdao.ccsp.management.modules.swarm.form.ContainerDeployForm;
import tech.tongdao.ccsp.management.modules.swarm.form.ContainerInstanceForm;
import tech.tongdao.ccsp.management.modules.swarm.form.DockerImageForm;
import tech.tongdao.ccsp.management.modules.swarm.mapper.ContainerInstanceRepository;
import tech.tongdao.ccsp.management.modules.swarm.service.*;
import tech.tongdao.ccsp.management.modules.swarm.utils.SwarmPlacementHelper;
import tech.tongdao.ccsp.management.utils.DockerSwarmServiceManager;
import tech.tongdao.ccsp.management.utils.JsonUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 容器实例管理服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@org.springframework.stereotype.Service
@CacheConfig(cacheNames = {"container-instance-cache"})
public class ContainerInstanceServiceImpl extends ServiceImpl<ContainerInstanceRepository, ContainerInstanceEntity> implements ContainerInstanceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContainerInstanceServiceImpl.class);

    /**
     * 默认网络名称
     */
    private static final String DEFAULT_NETWORK_NAME = "ccsp-network";

    /**
     * 默认存储卷名称
     */
    private static final String DEFAULT_CONSOLE_DATA_VOLUME = "ccsp-console-data";
    private static final String DEFAULT_CONSOLE_LOGS_VOLUME = "ccsp-console-logs";
    private static final String DEFAULT_CONSOLE_CONFIG_VOLUME = "ccsp-console-config";

    @Autowired
    private ContainerInstanceRepository containerInstanceRepository;

    @Autowired
    private DockerImageService dockerImageService;

    @Autowired
    private SwarmNodeService swarmNodeService;

    @Autowired
    private SwarmDataAggregationService swarmDataAggregationService;

    @Autowired
    private DockerSwarmServiceManager dockerSwarmServiceManager;

    @Autowired
    private SwarmEntityMapper swarmEntityMapper;

    @Resource
    private ApplicationService applicationService;

    @Autowired
    private ContainerDeviceResourceService containerDeviceResourceService;

    @Autowired
    private PortAllocationService portAllocationService;

    @Override
    public Pager<ContainerInstanceForm> listPage(ContainerInstanceForm form) {
        try {
            LOGGER.info("分页查询容器实例列表，查询条件：{}", form);

            // 使用MyBatis-Plus Lambda查询构建查询条件
            LambdaQueryWrapper<ContainerInstanceEntity> wrapper = new LambdaQueryWrapper<>();

            if (StringUtils.isNotBlank(form.getContainerName())) {
                wrapper.like(ContainerInstanceEntity::getContainerName, form.getContainerName());
            }
            if (StringUtils.isNotBlank(form.getImageName())) {
                wrapper.like(ContainerInstanceEntity::getImageName, form.getImageName());
            }
            if (StringUtils.isNotBlank(form.getStatus())) {
                wrapper.eq(ContainerInstanceEntity::getStatus, form.getStatus());
            }
            if (form.getImageId() != null) {
                wrapper.eq(ContainerInstanceEntity::getImageId, form.getImageId());
            }
            if (form.getNodeId() != null) {
                wrapper.eq(ContainerInstanceEntity::getNodeId, form.getNodeId());
            }
            if (form.getDeployedBy() != null) {
                wrapper.eq(ContainerInstanceEntity::getDeployedBy, form.getDeployedBy());
            }
            if (form.getApplicationId() != null) {
                wrapper.eq(ContainerInstanceEntity::getApplicationId, form.getApplicationId());
            }

            // 按设备资源类型筛选（通过关联表查询）
            if (StringUtils.isNotBlank(form.getDeviceResourceType())) {
                // 子查询：查找使用指定设备类型的容器ID列表
                wrapper.inSql(ContainerInstanceEntity::getId,
                    "SELECT DISTINCT container_instance_id FROM gmmid_container_device_resource_rel " +
                    "WHERE device_resource_type = '" + form.getDeviceResourceType() + "' AND status = 'active'");
            }

            // 添加排序
            wrapper.orderByDesc(ContainerInstanceEntity::getCreateTime);

            // 分页查询
            Page<ContainerInstanceEntity> page = new Page<>(form.getPage(), form.getPageSize());
            IPage<ContainerInstanceEntity> result = this.page(page, wrapper);

            // 使用MapStruct转换Entity到Form
            List<ContainerInstanceForm> formList = swarmEntityMapper.toContainerInstanceFormList(result.getRecords());

            for (ContainerInstanceForm containerInstanceForm : formList) {
                if (containerInstanceForm.getApplicationId() != null) {
                    ApplicationEntity application = applicationService.getById(containerInstanceForm.getApplicationId());
                    if (application != null) {
                        containerInstanceForm.setApplicationName(application.getName());
                    }
                }
            }

            // 数据聚合
            List<ContainerInstanceForm> aggregatedList = swarmDataAggregationService.aggregateContainerData(formList);

            LOGGER.info("查询到{}条容器实例记录", result.getTotal());
            return new Pager<>(aggregatedList, (int) result.getTotal(), form.getPage(), form.getPageSize());

        } catch (Exception e) {
            LOGGER.error("分页查询容器实例列表失败", e);
            throw new BusinessException("查询容器实例列表失败：" + e.getMessage());
        }
    }

    @Override
    @Cacheable(key = "#id", unless = "#result == null")
    public ContainerInstanceForm getById(Long id) {
        try {
            LOGGER.info("根据ID查询容器实例详情，ID：{}", id);

            if (id == null) {
                throw new BusinessException("容器实例ID不能为空");
            }

            // 使用MyBatis-Plus的getById方法
            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                LOGGER.warn("未找到ID为{}的容器实例", id);
                throw new BusinessException("容器实例不存在");
            }

            // 使用MapStruct转换Entity到Form
            ContainerInstanceForm result = swarmEntityMapper.toContainerInstanceForm(entity);

            // 数据聚合
            result = swarmDataAggregationService.aggregateContainerData(result);

            LOGGER.info("查询到容器实例：{}", result.getContainerName());
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("根据ID查询容器实例详情失败，ID：{}", id, e);
            throw new BusinessException("查询容器实例详情失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContainerInstanceForm deployContainer(ContainerDeployForm deployForm) {
        try {
            LOGGER.info("开始部署容器，部署配置：{}", deployForm);
            //验证APP
            ApplicationEntity application = applicationService.getById(deployForm.getApplicationId());
            if (application == null) {
                throw new BusinessException("应用不存在");
            }
            // 获取镜像信息
            DockerImageForm imageInfo = dockerImageService.getById(deployForm.getImageId());
            if (imageInfo == null) {
                throw new BusinessException("指定的镜像不存在");
            }

            // 自动填充镜像名称和标签
            if (deployForm.getImageName() == null || deployForm.getImageName().trim().isEmpty() || "null".equals(deployForm.getImageName())) {
                deployForm.setImageName(imageInfo.getName());
                LOGGER.debug("自动填充镜像名称：{}", imageInfo.getName());
            }
            if (deployForm.getImageTag() == null || deployForm.getImageTag().trim().isEmpty() || "null".equals(deployForm.getImageTag())) {
                deployForm.setImageTag(imageInfo.getTag());
                LOGGER.debug("自动填充镜像标签：{}", imageInfo.getTag());
            }

            // 验证部署配置
            if (!deployForm.isValidDeployConfig()) {
                String errorDetails = getValidationErrorDetails(deployForm);
                LOGGER.error("部署配置验证失败，详细信息：{}", errorDetails);
                throw new BusinessException("部署配置无效：" + errorDetails);
            }

            // 构建Docker Swarm服务配置
            DockerSwarmServiceManager.ServiceConfig serviceConfig = buildServiceConfig(deployForm, imageInfo);

            // 创建Docker Swarm服务
            String serviceId = dockerSwarmServiceManager.createService(serviceConfig);

            // 保存容器实例记录
            ContainerInstanceEntity entity = createContainerInstanceEntity(deployForm, imageInfo, serviceId);
            save(entity);

            // 处理设备资源关联
            if (deployForm.getDeviceResourceConfig() != null) {
                ContainerDeployForm.DeviceResourceConfig deviceConfig = deployForm.getDeviceResourceConfig();
                if (deviceConfig.getDeviceResourceIds() != null && !deviceConfig.getDeviceResourceIds().isEmpty()) {
                    boolean allocated = containerDeviceResourceService.allocateDeviceResources(
                            entity.getId(),
                            deviceConfig.getDeviceResourceIds(),
                            deviceConfig.getDeviceResourceType(),
                            deviceConfig.getAllocationType(),
                            deviceConfig.getConfigData(),
                            deployForm.getDeployedBy()
                    );
                    if (!allocated) {
                        LOGGER.warn("设备资源分配失败，容器ID：{}，设备资源：{}", entity.getId(), deviceConfig.getDeviceResourceIds());
                    }
                }
            }

            // 更新端口分配记录的容器ID（如果之前预分配了端口）
            if (deployForm.getEnvironmentVars() != null &&
                deployForm.getEnvironmentVars().containsKey("TEMP_CONTAINER_ID")) {

                String tempContainerIdStr = deployForm.getEnvironmentVars().get("TEMP_CONTAINER_ID");
                String allocatedPortStr = deployForm.getEnvironmentVars().get("ALLOCATED_PORT");

                if (tempContainerIdStr != null && allocatedPortStr != null) {
                    try {
                        Long tempContainerId = Long.parseLong(tempContainerIdStr);
                        Integer allocatedPort = Integer.parseInt(allocatedPortStr);

                        // 更新端口分配记录的容器ID
                        portAllocationService.updateContainerId(tempContainerId, entity.getId());

                        LOGGER.info("已更新端口分配记录，临时ID：{} -> 真实ID：{}，端口：{}",
                                tempContainerId, entity.getId(), allocatedPort);

                        // 清理临时环境变量
                        deployForm.getEnvironmentVars().remove("TEMP_CONTAINER_ID");
                        deployForm.getEnvironmentVars().remove("ALLOCATED_PORT");

                    } catch (NumberFormatException e) {
                        LOGGER.error("解析端口分配信息失败", e);
                    }
                }
            }

            // 使用MapStruct转换为表单对象并返回
            ContainerInstanceForm result = swarmEntityMapper.toContainerInstanceForm(entity);

            LOGGER.info("容器部署成功，容器名称：{}，服务ID：{}", deployForm.getContainerName(), serviceId);
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("部署容器失败，部署配置：{}", deployForm, e);
            // 尝试清理已创建的资源
            rollbackDeployment(deployForm.getContainerName());
            throw new BusinessException("容器部署失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public void startContainer(Long id) {
        try {
            LOGGER.info("启动容器，ID：{}", id);

            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("容器实例不存在");
            }

            if ("running".equals(entity.getStatus())) {
                LOGGER.info("容器已经在运行中，无需启动，容器名称：{}", entity.getContainerName());
                return;
            }

            // 通过扩缩容到1个副本来启动服务
            if (StringUtils.isNotBlank(entity.getServiceId())) {
                dockerSwarmServiceManager.scaleService(entity.getServiceId(), 1);

                // 更新容器状态
                entity.setStatus("running");
                entity.setStartTime(LocalDateTime.now());
                entity.setStopTime(null);
                updateById(entity);

                LOGGER.info("容器启动成功，容器名称：{}", entity.getContainerName());
            } else {
                throw new BusinessException("容器服务ID为空，无法启动");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("启动容器失败，ID：{}", id, e);
            throw new BusinessException("启动容器失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public void stopContainer(Long id) {
        try {
            LOGGER.info("停止容器，ID：{}", id);

            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("容器实例不存在");
            }

            if ("stopped".equals(entity.getStatus())) {
                LOGGER.info("容器已经停止，无需操作，容器名称：{}", entity.getContainerName());
                return;
            }

            // 通过扩缩容到0个副本来停止服务
            if (StringUtils.isNotBlank(entity.getServiceId())) {
                dockerSwarmServiceManager.scaleService(entity.getServiceId(), 0);

                // 更新容器状态
                entity.setStatus("stopped");
                entity.setStopTime(LocalDateTime.now());
                updateById(entity);

                LOGGER.info("容器停止成功，容器名称：{}", entity.getContainerName());
            } else {
                throw new BusinessException("容器服务ID为空，无法停止");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("停止容器失败，ID：{}", id, e);
            throw new BusinessException("停止容器失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public void restartContainer(Long id) {
        try {
            LOGGER.info("重启容器，ID：{}", id);

            // 先停止再启动
            stopContainer(id);
            Thread.sleep(2000); // 等待2秒确保停止完成
            startContainer(id);

            LOGGER.info("容器重启成功，ID：{}", id);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("重启容器失败，ID：{}", id, e);
            throw new BusinessException("重启容器失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public void deleteContainer(Long id) {
        try {
            LOGGER.info("删除容器，ID：{}", id);

            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("容器实例不存在");
            }

            // 删除Docker Swarm服务
            if (StringUtils.isNotBlank(entity.getServiceId())) {
                try {
                    dockerSwarmServiceManager.removeService(entity.getServiceId());
                    LOGGER.info("Docker服务删除成功，服务ID：{}", entity.getServiceId());
                } catch (Exception e) {
                    LOGGER.warn("删除Docker服务失败，服务ID：{}，错误：{}", entity.getServiceId(), e.getMessage());
                    // 继续删除数据库记录，不因为Docker服务删除失败而中断
                }
            }

            // 释放设备资源关联
            containerDeviceResourceService.releaseDeviceResources(id);
            LOGGER.info("释放容器设备资源关联成功，容器ID：{}", id);

            // 释放分配的端口
            boolean portReleased = portAllocationService.releasePort(id);
            if (portReleased) {
                LOGGER.info("释放容器端口成功，容器ID：{}", id);
            } else {
                LOGGER.warn("释放容器端口失败或未分配端口，容器ID：{}", id);
            }

            // 删除数据库记录
            removeById(id);

            LOGGER.info("容器删除成功，容器名称：{}", entity.getContainerName());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("删除容器失败，ID：{}", id, e);
            throw new BusinessException("删除容器失败：" + e.getMessage());
        }
    }

    @Override
    @CacheEvict(allEntries = true)
    public void syncContainerStatus() {
        try {
            LOGGER.info("开始同步容器状态");

            // 获取所有容器实例
            List<ContainerInstanceEntity> containers = list();
            if (containers.isEmpty()) {
                LOGGER.info("没有容器实例需要同步");
                return;
            }

            // 获取所有Docker服务
            List<Service> services = dockerSwarmServiceManager.listServices();
            Map<String, Service> serviceMap = services.stream()
                    .collect(Collectors.toMap(Service::getId, service -> service));

            // 更新容器状态
            List<ContainerInstanceEntity> updatedContainers = new ArrayList<>();
            for (ContainerInstanceEntity container : containers) {
                if (StringUtils.isNotBlank(container.getServiceId())) {
                    Service service = serviceMap.get(container.getServiceId());
                    if (service != null) {
                        String newStatus = determineContainerStatus(service);
                        if (!newStatus.equals(container.getStatus())) {
                            container.setStatus(newStatus);
                            container.setUpdateTime(LocalDateTime.now());
                            updatedContainers.add(container);
                        }
                    } else {
                        // 服务不存在，标记为失败状态
                        if (!"failed".equals(container.getStatus())) {
                            container.setStatus("failed");
                            container.setUpdateTime(LocalDateTime.now());
                            updatedContainers.add(container);
                        }
                    }
                }
            }

            // 批量更新状态
            if (!updatedContainers.isEmpty()) {
                this.updateBatchById(updatedContainers);
                LOGGER.info("同步容器状态完成，更新了{}个容器的状态", updatedContainers.size());
            } else {
                LOGGER.info("所有容器状态都是最新的，无需更新");
            }

        } catch (Exception e) {
            LOGGER.error("同步容器状态失败", e);
            throw new BusinessException("同步容器状态失败：" + e.getMessage());
        }
    }

    @Override
    public List<ContainerInstanceForm> getContainersByImage(Long imageId) {
        try {
            LOGGER.info("根据镜像ID查询容器实例列表，镜像ID：{}", imageId);

            if (imageId == null) {
                throw new BusinessException("镜像ID不能为空");
            }

            // 使用MyBatis-Plus Lambda查询
            LambdaQueryWrapper<ContainerInstanceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerInstanceEntity::getImageId, imageId)
                    .orderByDesc(ContainerInstanceEntity::getCreateTime);

            List<ContainerInstanceEntity> entities = this.list(wrapper);
            List<ContainerInstanceForm> result = swarmEntityMapper.toContainerInstanceFormList(entities);

            LOGGER.info("查询到{}个使用镜像ID为{}的容器实例", result.size(), imageId);
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("根据镜像ID查询容器实例列表失败，镜像ID：{}", imageId, e);
            throw new BusinessException("查询容器实例列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<ContainerInstanceForm> getContainersByNode(Long nodeId) {
        try {
            LOGGER.info("根据节点ID查询容器实例列表，节点ID：{}", nodeId);

            if (nodeId == null) {
                throw new BusinessException("节点ID不能为空");
            }

            // 使用MyBatis-Plus Lambda查询
            LambdaQueryWrapper<ContainerInstanceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerInstanceEntity::getNodeId, nodeId)
                    .orderByDesc(ContainerInstanceEntity::getCreateTime);

            List<ContainerInstanceEntity> entities = this.list(wrapper);
            List<ContainerInstanceForm> result = swarmEntityMapper.toContainerInstanceFormList(entities);

            LOGGER.info("查询到{}个部署在节点ID为{}的容器实例", result.size(), nodeId);
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("根据节点ID查询容器实例列表失败，节点ID：{}", nodeId, e);
            throw new BusinessException("查询容器实例列表失败：" + e.getMessage());
        }
    }

    @Override
    @Cacheable(key = "'running-containers'")
    public List<ContainerInstanceForm> getRunningContainers() {
        try {
            LOGGER.info("查询运行中的容器实例列表");

            // 使用MyBatis-Plus Lambda查询
            LambdaQueryWrapper<ContainerInstanceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerInstanceEntity::getStatus, "running")
                    .orderByDesc(ContainerInstanceEntity::getStartTime);

            List<ContainerInstanceEntity> entities = this.list(wrapper);
            List<ContainerInstanceForm> result = swarmEntityMapper.toContainerInstanceFormList(entities);

            LOGGER.info("查询到{}个运行中的容器实例", result.size());
            return result;

        } catch (Exception e) {
            LOGGER.error("查询运行中的容器实例列表失败", e);
            throw new BusinessException("查询运行中的容器实例列表失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public void scaleContainer(Long id, Integer replicas) {
        try {
            LOGGER.info("扩缩容容器，ID：{}，目标副本数：{}", id, replicas);

            if (replicas == null || replicas < 0) {
                throw new BusinessException("副本数不能为空且不能小于0");
            }

            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("容器实例不存在");
            }

            if (StringUtils.isNotBlank(entity.getServiceId())) {
                dockerSwarmServiceManager.scaleService(entity.getServiceId(), replicas);

                // 更新容器状态
                String newStatus = replicas > 0 ? "running" : "stopped";
                entity.setStatus(newStatus);
                entity.setUpdateTime(LocalDateTime.now());
                if (replicas > 0) {
                    entity.setStartTime(LocalDateTime.now());
                    entity.setStopTime(null);
                } else {
                    entity.setStopTime(LocalDateTime.now());
                }
                updateById(entity);

                LOGGER.info("容器扩缩容成功，容器名称：{}，副本数：{}", entity.getContainerName(), replicas);
            } else {
                throw new BusinessException("容器服务ID为空，无法扩缩容");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("扩缩容容器失败，ID：{}，目标副本数：{}", id, replicas, e);
            throw new BusinessException("扩缩容容器失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#id")
    public void updateContainerImage(Long id, Long newImageId) {
        try {
            LOGGER.info("更新容器镜像，容器ID：{}，新镜像ID：{}", id, newImageId);

            if (newImageId == null) {
                throw new BusinessException("新镜像ID不能为空");
            }

            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("容器实例不存在");
            }

            // 获取新镜像信息
            DockerImageForm newImageInfo = dockerImageService.getById(newImageId);
            if (newImageInfo == null) {
                throw new BusinessException("指定的新镜像不存在");
            }

            String newImageName = newImageInfo.getName() + ":" + newImageInfo.getTag();

            if (StringUtils.isNotBlank(entity.getServiceId())) {
                dockerSwarmServiceManager.updateServiceImage(entity.getServiceId(), newImageName);

                // 更新容器实例记录
                entity.setImageId(newImageId);
                entity.setImageName(newImageInfo.getName());
                entity.setImageTag(newImageInfo.getTag());
                entity.setUpdateTime(LocalDateTime.now());
                updateById(entity);

                LOGGER.info("容器镜像更新成功，容器名称：{}，新镜像：{}", entity.getContainerName(), newImageName);
            } else {
                throw new BusinessException("容器服务ID为空，无法更新镜像");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("更新容器镜像失败，容器ID：{}，新镜像ID：{}", id, newImageId, e);
            throw new BusinessException("更新容器镜像失败：" + e.getMessage());
        }
    }

    @Override
    public String getContainerLogs(Long id, Integer lines) {
        try {
            LOGGER.info("获取容器日志，容器ID：{}，日志行数：{}", id, lines);

            ContainerInstanceEntity entity = super.getById(id);
            if (entity == null) {
                throw new BusinessException("容器实例不存在");
            }

            if (StringUtils.isBlank(entity.getServiceId())) {
                throw new BusinessException("容器服务ID为空，无法获取日志");
            }

            // 获取服务的任务列表
            List<Task> tasks = dockerSwarmServiceManager.getServiceTasks(entity.getServiceId());
            if (tasks.isEmpty()) {
                return "暂无日志信息";
            }

            // 这里简化处理，实际应该调用Docker API获取容器日志
            // 由于DockerSwarmServiceManager没有提供日志获取方法，这里返回任务状态信息
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("=== 容器任务状态信息 ===\n");
            for (Task task : tasks) {
                logBuilder.append("任务ID: ").append(task.getId()).append("\n");
                if (task.getStatus() != null) {
                    logBuilder.append("状态: ").append(task.getStatus().getState()).append("\n");
                    logBuilder.append("时间: ").append(task.getStatus().getTimestamp()).append("\n");
                }
                logBuilder.append("---\n");
            }

            LOGGER.info("获取容器日志成功，容器名称：{}", entity.getContainerName());
            return logBuilder.toString();

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("获取容器日志失败，容器ID：{}", id, e);
            throw new BusinessException("获取容器日志失败：" + e.getMessage());
        }
    }

    @Override
    public ContainerInstanceForm getContainerStats(Long id) {
        try {
            LOGGER.info("获取容器统计信息，容器ID：{}", id);

            ContainerInstanceForm container = getById(id);
            if (container == null) {
                throw new BusinessException("容器实例不存在");
            }

            if (StringUtils.isBlank(container.getServiceId())) {
                return container;
            }

            // 获取服务任务统计信息
            Map<String, Integer> taskStats = dockerSwarmServiceManager.getServiceTaskStats(container.getServiceId());

            // 将统计信息添加到容器信息中（这里简化处理，实际可以扩展ContainerInstanceForm添加统计字段）
            LOGGER.info("获取容器统计信息成功，容器名称：{}，任务统计：{}", container.getContainerName(), taskStats);
            return container;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("获取容器统计信息失败，容器ID：{}", id, e);
            throw new BusinessException("获取容器统计信息失败：" + e.getMessage());
        }
    }

    // --- 私有辅助方法 ---

    /**
     * 构建Docker Swarm服务配置
     */
    private DockerSwarmServiceManager.ServiceConfig buildServiceConfig(ContainerDeployForm deployForm, DockerImageForm imageInfo) {
        String fullImageName = imageInfo.getName() + ":" + imageInfo.getTag();

        DockerSwarmServiceManager.ServiceConfig config = new DockerSwarmServiceManager.ServiceConfig()
                .setServiceName(deployForm.getContainerName())
                .setImage(fullImageName)
                .setReplicas(deployForm.getReplicas() != null ? deployForm.getReplicas() : 1);

        // 设置环境变量
        Map<String, String> envVars = new HashMap<>();
        if (deployForm.getEnvironmentVars() != null && !deployForm.getEnvironmentVars().isEmpty()) {
            envVars.putAll(deployForm.getEnvironmentVars());
        }

        // 添加console.instance.appId环境变量
        if (deployForm.getApplicationId() != null) {
            envVars.put("console.instance.appId", deployForm.getApplicationId().toString());
            LOGGER.info("为容器{}添加console.instance.appId环境变量：{}", deployForm.getContainerName(), deployForm.getApplicationId());
        }

        // Console服务固定使用8300端口
        final Integer CONSOLE_SERVICE_PORT = 8300;
        deployForm.setServicePort(CONSOLE_SERVICE_PORT);

        LOGGER.info("为容器{}设置Console服务端口：{}", deployForm.getContainerName(), CONSOLE_SERVICE_PORT);

        // 为Docker Swarm服务分配统一的外部端口（所有副本共享）
        if (deployForm.getServicePort() != null) {
            // 使用服务名作为端口分配的标识，确保同一服务的所有副本共享同一端口
            String serviceKey = deployForm.getContainerName();

            // 检查是否已经为这个服务分配了端口
            Integer allocatedPort = getServiceAllocatedPort(serviceKey);

            if (allocatedPort == null) {
                // 首次部署，分配新端口
                Long tempContainerId = System.currentTimeMillis(); // 使用时间戳作为临时ID

                allocatedPort = portAllocationService.allocatePortWithDetails(
                        tempContainerId,
                        deployForm.getContainerName(),
                        deployForm.getServicePort(),
                        deployForm.getDeployedBy(),
                        null,
                        null
                );

                if (allocatedPort != null) {
                    LOGGER.info("为服务{}首次分配外部端口：{}", serviceKey, allocatedPort);
                    // 保存服务端口映射信息到环境变量和deployForm中
                    envVars.put("TEMP_CONTAINER_ID", tempContainerId.toString());
                    envVars.put("ALLOCATED_PORT", allocatedPort.toString());

                    // 同时保存到deployForm的环境变量中，用于后续更新端口分配记录
                    if (deployForm.getEnvironmentVars() == null) {
                        deployForm.setEnvironmentVars(new HashMap<>());
                    }
                    deployForm.getEnvironmentVars().put("TEMP_CONTAINER_ID", tempContainerId.toString());
                    deployForm.getEnvironmentVars().put("ALLOCATED_PORT", allocatedPort.toString());
                } else {
                    LOGGER.warn("为服务{}分配外部端口失败", serviceKey);
                    return config;
                }
            } else {
                LOGGER.info("服务{}使用已分配的外部端口：{}", serviceKey, allocatedPort);
            }

            // 创建端口映射配置（Docker Swarm模式：所有副本共享同一外部端口）
            List<ContainerDeployForm.PortMapping> portMappings = new ArrayList<>();
            ContainerDeployForm.PortMapping portMapping = new ContainerDeployForm.PortMapping();
            portMapping.setContainerPort(deployForm.getServicePort()); // 8300
            portMapping.setHostPort(allocatedPort); // 共享的外部端口
            portMapping.setProtocol("tcp");
            portMappings.add(portMapping);
            deployForm.setPortMappings(portMappings);

            LOGGER.info("为服务{}配置端口映射：{}:tcp -> {}（Swarm内置负载均衡）",
                    serviceKey, allocatedPort, deployForm.getServicePort());
        }

        if (!envVars.isEmpty()) {
            config.setEnvironmentVariables(envVars);
        }

        // 设置网络
        if (deployForm.getNetworks() != null && !deployForm.getNetworks().isEmpty()) {
            config.setNetworks(deployForm.getNetworks());
        } else {
            // 如果没有指定网络，使用默认的ccsp-network
            List<String> defaultNetworks = new ArrayList<>();
            defaultNetworks.add(DEFAULT_NETWORK_NAME);
            config.setNetworks(defaultNetworks);
            LOGGER.info("为容器{}设置默认网络：{}", deployForm.getContainerName(), DEFAULT_NETWORK_NAME);
        }

        // 设置约束条件和分布策略
        List<String> constraints = new ArrayList<>();
        if (deployForm.getConstraints() != null && !deployForm.getConstraints().isEmpty()) {
            constraints.addAll(deployForm.getConstraints());
        }

        // 添加默认分布策略：确保副本分散在不同节点上
        // 当副本数大于1时，添加分布约束
        Integer replicas = deployForm.getReplicas() != null ? deployForm.getReplicas() : 1;
        if (replicas > 1) {
            // 使用分布策略生成约束
            List<String> distributionConstraints = SwarmPlacementHelper.generateDistributionConstraints(
                    replicas, SwarmPlacementHelper.DistributionStrategy.SPREAD_ACROSS_NODES);
            constraints.addAll(distributionConstraints);

            // 设置分布偏好：优先分散到不同节点
            config.setSpreadPreference("node.id");

            LOGGER.info("为服务 {} 设置分布策略，副本数：{}，分布偏好：node.id，附加约束：{}",
                    deployForm.getContainerName(), replicas, distributionConstraints);
        }

        if (!constraints.isEmpty()) {
            config.setConstraints(constraints);
        }

        // 设置资源限制
        if (deployForm.getResourceLimits() != null) {
            ContainerDeployForm.ResourceLimits limits = deployForm.getResourceLimits();
            if (limits.getCpuLimit() != null && limits.getCpuLimit() > 0) {
                config.setCpuNano((long) (limits.getCpuLimit() * 1_000_000_000L)); // 转换为纳秒
            }
            if (limits.getMemoryMb() != null && limits.getMemoryMb() > 0) {
                config.setMemoryBytes(limits.getMemoryMb() * 1024 * 1024); // 转换为字节
            }
        }

        // 设置端口映射
        if (deployForm.getPortMappings() != null && !deployForm.getPortMappings().isEmpty()) {
            List<PortConfig> portConfigs = deployForm.getPortMappings().stream()
                    .map(this::convertToPortConfig)
                    .collect(Collectors.toList());
            config.setPortConfigs(portConfigs);
        }

        // 设置存储卷挂载
        List<ContainerDeployForm.VolumeMount> volumeMounts = new ArrayList<>();
        if (deployForm.getVolumeMounts() != null && !deployForm.getVolumeMounts().isEmpty()) {
            volumeMounts.addAll(deployForm.getVolumeMounts());
        } else {
            // 为Console类型的容器添加默认存储卷挂载
            volumeMounts.addAll(createDefaultVolumeMounts(deployForm));
        }

        if (!volumeMounts.isEmpty()) {
            List<com.github.dockerjava.api.model.Mount> mounts = volumeMounts.stream()
                    .map(this::convertToMount)
                    .collect(Collectors.toList());
            config.setMounts(mounts);
            LOGGER.info("为容器{}设置存储卷挂载：{}", deployForm.getContainerName(),
                    volumeMounts.stream()
                            .map(vm -> vm.getSource() + ":" + vm.getTarget())
                            .collect(Collectors.joining(", ")));
        }

        return config;
    }

    /**
     * 转换端口映射配置
     */
    private PortConfig convertToPortConfig(ContainerDeployForm.PortMapping mapping) {
        PortConfig portConfig = new PortConfig();
        portConfig.withTargetPort(mapping.getContainerPort());
        if (mapping.getHostPort() != null) {
            portConfig.withPublishedPort(mapping.getHostPort());
        }
        portConfig.withProtocol(PortConfigProtocol.valueOf(mapping.getProtocol().toUpperCase()));
        return portConfig;
    }

    /**
     * 转换存储卷挂载配置
     */
    private com.github.dockerjava.api.model.Mount convertToMount(ContainerDeployForm.VolumeMount volumeMount) {
        com.github.dockerjava.api.model.Mount mount = new com.github.dockerjava.api.model.Mount();
        mount.withSource(volumeMount.getSource());
        mount.withTarget(volumeMount.getTarget());
        mount.withReadOnly(volumeMount.getReadOnly() != null ? volumeMount.getReadOnly() : false);

        // 设置挂载类型
        if ("bind".equalsIgnoreCase(volumeMount.getType())) {
            mount.withType(com.github.dockerjava.api.model.MountType.BIND);
        } else if ("volume".equalsIgnoreCase(volumeMount.getType())) {
            mount.withType(com.github.dockerjava.api.model.MountType.VOLUME);
        } else if ("tmpfs".equalsIgnoreCase(volumeMount.getType())) {
            mount.withType(com.github.dockerjava.api.model.MountType.TMPFS);
        } else {
            // 默认使用bind类型
            mount.withType(com.github.dockerjava.api.model.MountType.BIND);
        }

        return mount;
    }

    /**
     * 创建默认存储卷挂载配置
     * 为Console类型的容器添加默认的数据、日志和配置存储卷
     */
    private List<ContainerDeployForm.VolumeMount> createDefaultVolumeMounts(ContainerDeployForm deployForm) {
        List<ContainerDeployForm.VolumeMount> defaultMounts = new ArrayList<>();

        // 检查是否是Console类型的容器（通过镜像名称或容器名称判断）
        boolean isConsoleContainer = isConsoleContainer(deployForm);

        if (isConsoleContainer) {
            // 添加数据存储卷
            defaultMounts.add(new ContainerDeployForm.VolumeMount(
                    DEFAULT_CONSOLE_DATA_VOLUME, "/app/data", "volume", false));

            // 添加日志存储卷
            defaultMounts.add(new ContainerDeployForm.VolumeMount(
                    DEFAULT_CONSOLE_LOGS_VOLUME, "/app/logs", "volume", false));

            // 添加配置存储卷
            defaultMounts.add(new ContainerDeployForm.VolumeMount(
                    DEFAULT_CONSOLE_CONFIG_VOLUME, "/app/config", "volume", true));

            LOGGER.info("为Console容器{}添加默认存储卷挂载", deployForm.getContainerName());
        }

        return defaultMounts;
    }

    /**
     * 判断是否是Console类型的容器
     */
    private boolean isConsoleContainer(ContainerDeployForm deployForm) {
        // 通过镜像名称判断
        if (deployForm.getImageName() != null &&
            (deployForm.getImageName().toLowerCase().contains("console") ||
             deployForm.getImageName().toLowerCase().contains("ccsp"))) {
            return true;
        }

        // 通过容器名称判断
        if (deployForm.getContainerName() != null &&
            deployForm.getContainerName().toLowerCase().contains("console")) {
            return true;
        }

        return false;
    }

    /**
     * 创建容器实例实体对象
     */
    private ContainerInstanceEntity createContainerInstanceEntity(ContainerDeployForm deployForm, DockerImageForm imageInfo, String serviceId) {
        ContainerInstanceEntity entity = new ContainerInstanceEntity();
        entity.setApplicationId(deployForm.getApplicationId());
        entity.setServiceId(serviceId);
        entity.setContainerName(deployForm.getContainerName());
        entity.setImageId(deployForm.getImageId());
        entity.setImageName(imageInfo.getName());
        entity.setImageTag(imageInfo.getTag());
        entity.setNodeId(deployForm.getNodeId());
        entity.setNodeName(deployForm.getNodeName());
        entity.setStatus("pending");
        entity.setDeployedBy(deployForm.getDeployedBy());
        entity.setDeployTime(LocalDateTime.now());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());

        // 序列化配置信息为JSON
        if (deployForm.getPortMappings() != null) {
            entity.setPortMappings(JsonUtils.toJson(deployForm.getPortMappings()));
        }
        if (deployForm.getEnvironmentVars() != null) {
            entity.setEnvironmentVars(JsonUtils.toJson(deployForm.getEnvironmentVars()));
        }
        if (deployForm.getVolumeMounts() != null) {
            entity.setVolumeMounts(JsonUtils.toJson(deployForm.getVolumeMounts()));
        }
        if (deployForm.getResourceLimits() != null) {
            entity.setResourceLimits(JsonUtils.toJson(deployForm.getResourceLimits()));
        }

        // 保存完整的部署配置
        entity.setDeployConfig(JsonUtils.toJson(deployForm));

        return entity;
    }

    /**
     * 根据Docker服务状态确定容器状态
     */
    private String determineContainerStatus(Service service) {
        if (service.getSpec() == null || service.getSpec().getMode() == null) {
            return "unknown";
        }

        // 获取期望副本数
        Long desiredReplicas = 0L;
        if (service.getSpec().getMode().getReplicated() != null) {
            desiredReplicas = service.getSpec().getMode().getReplicated().getReplicas();
        }

        if (desiredReplicas == 0) {
            return "stopped";
        }

        // 这里简化处理，实际应该检查任务状态
        return "running";
    }

    /**
     * 回滚部署操作
     */
    private void rollbackDeployment(String serviceName) {
        try {
            if (StringUtils.isNotBlank(serviceName)) {
                LOGGER.info("尝试回滚部署，删除服务：{}", serviceName);
                dockerSwarmServiceManager.removeService(serviceName);
                LOGGER.info("回滚部署成功，已删除服务：{}", serviceName);
            }
        } catch (Exception e) {
            LOGGER.error("回滚部署失败，服务名称：{}，错误：{}", serviceName, e.getMessage());
        }
    }

    @Override
    public Integer countContainersByNodeId(Long nodeId) {
        try {
            if (nodeId == null) {
                return 0;
            }
            return containerInstanceRepository.countContainersByNodeId(nodeId);
        } catch (Exception e) {
            LOGGER.error("统计节点容器数量失败，nodeId: {}", nodeId, e);
            return 0;
        }
    }

    @Override
    public Integer countContainersByImageId(Long imageId) {
        try {
            if (imageId == null) {
                return 0;
            }
            return containerInstanceRepository.countContainersByImageId(imageId);
        } catch (Exception e) {
            LOGGER.error("统计镜像容器数量失败，imageId: {}", imageId, e);
            return 0;
        }
    }

    /**
     * 获取验证错误详情
     */
    private String getValidationErrorDetails(ContainerDeployForm deployForm) {
        StringBuilder errors = new StringBuilder();

        // 检查必填字段
        if (deployForm.getContainerName() == null || deployForm.getContainerName().trim().isEmpty()) {
            errors.append("容器名称不能为空; ");
        }
        if (deployForm.getImageId() == null) {
            errors.append("镜像ID不能为空; ");
        }
        if (deployForm.getImageName() == null || deployForm.getImageName().trim().isEmpty()) {
            errors.append("镜像名称不能为空; ");
        }
        if (deployForm.getImageTag() == null || deployForm.getImageTag().trim().isEmpty()) {
            errors.append("镜像标签不能为空; ");
        }

        // 检查副本数量
        if (deployForm.getReplicas() == null || deployForm.getReplicas() < 1 || deployForm.getReplicas() > 100) {
            errors.append(String.format("副本数量必须在1-100之间，当前值: %s; ", deployForm.getReplicas()));
        }

        // 检查端口映射
        if (deployForm.getPortMappings() != null && !deployForm.getPortMappings().isEmpty()) {
            for (int i = 0; i < deployForm.getPortMappings().size(); i++) {
                ContainerDeployForm.PortMapping mapping = deployForm.getPortMappings().get(i);
                if (mapping.getContainerPort() == null || mapping.getContainerPort() < 1 || mapping.getContainerPort() > 65535) {
                    errors.append(String.format("端口映射[%d]容器端口无效: %s; ", i, mapping.getContainerPort()));
                }
                if (mapping.getHostPort() != null && (mapping.getHostPort() < 1 || mapping.getHostPort() > 65535)) {
                    errors.append(String.format("端口映射[%d]主机端口无效: %s; ", i, mapping.getHostPort()));
                }
            }
        }

        return errors.length() > 0 ? errors.toString() : "未知验证错误";
    }

    /**
     * 获取服务已分配的端口（通过容器名查找）
     */
    private Integer getServiceAllocatedPort(String serviceName) {
        try {
            // 查找同名服务的现有容器实例
            LambdaQueryWrapper<ContainerInstanceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContainerInstanceEntity::getContainerName, serviceName)
                    .eq(ContainerInstanceEntity::getStatus, "running")
                    .last("LIMIT 1");

            ContainerInstanceEntity existingContainer = this.getOne(wrapper);
            if (existingContainer != null) {
                // 获取该容器分配的端口
                return portAllocationService.getContainerPort(existingContainer.getId());
            }

            return null;
        } catch (Exception e) {
            LOGGER.error("获取服务已分配端口失败，服务名：{}", serviceName, e);
            return null;
        }
    }
}