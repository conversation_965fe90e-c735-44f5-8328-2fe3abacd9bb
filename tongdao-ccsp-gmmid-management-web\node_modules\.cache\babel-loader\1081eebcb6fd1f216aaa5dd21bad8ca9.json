{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1757058863660}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\babel.config.js", "mtime": 1699511114284}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gIkQ6L3dvcmtzcGFjZS9jY3NwX3YzL3RvbmdkYW8tY2NzcC1nbW1pZC1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheS5qcyI7CmltcG9ydCBfcmVnZW5lcmF0b3IgZnJvbSAiRDovd29ya3NwYWNlL2Njc3BfdjMvdG9uZ2Rhby1jY3NwLWdtbWlkLW1hbmFnZW1lbnQtd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvci5qcyI7CmltcG9ydCBfYXN5bmNUb0dlbmVyYXRvciBmcm9tICJEOi93b3Jrc3BhY2UvY2NzcF92My90b25nZGFvLWNjc3AtZ21taWQtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJEOi93b3Jrc3BhY2UvY2NzcF92My90b25nZGFvLWNjc3AtZ21taWQtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNsaWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLnRvLWZpeGVkLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmVudHJpZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nOwppbXBvcnQgeyBnZXRDb250YWluZXJMaXN0LCBkZXBsb3lDb250YWluZXIsIGdldFJlY29tbWVuZGVkU3RyYXRlZ3ksIHN0YXJ0Q29udGFpbmVyLCBzdG9wQ29udGFpbmVyLCByZXN0YXJ0Q29udGFpbmVyLCByZW1vdmVDb250YWluZXIsIGdldENvbnRhaW5lckxvZ3MsIHN5bmNDb250YWluZXJTdGF0dXMsIHNjYWxlQ29udGFpbmVyLCB1cGRhdGVDb250YWluZXJJbWFnZSwgY29uZmlnQ29udGFpbmVyUm91dGUgYXMgX2NvbmZpZ0NvbnRhaW5lclJvdXRlLCB1cGRhdGVDb250YWluZXJSb3V0ZSBhcyBfdXBkYXRlQ29udGFpbmVyUm91dGUsIGdldEF2YWlsYWJsZURldmljZVJlc291cmNlcywgZ2V0Q29udGFpbmVyRGV2aWNlUmVzb3VyY2VzLCBnZXRDb250YWluZXJBY2Nlc3NVcmwgfSBmcm9tICdAL2FwaS9kb2NrZXIvY29udGFpbmVyJzsKaW1wb3J0IHsgZ2V0SW1hZ2VMaXN0IH0gZnJvbSAnQC9hcGkvZG9ja2VyL2ltYWdlJzsKaW1wb3J0IHsgZmV0Y2hBcHBsaWNhdGlvbk9wdGlvbnMsIGZldGNoVXNlckFwcHMgfSBmcm9tICdAL2FwaS9hcHBsaWNhdGlvbic7CmltcG9ydCB7IGdldEF2YWlsYWJsZUhzbURldmljZXMsIGdldEhzbURldmljZURldGFpbCB9IGZyb20gJ0AvYXBpL2RvY2tlci9oc20nOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RvY2tlckNvbnRhaW5lckluZGV4JywKICBjb21wb25lbnRzOiB7CiAgICBQYWdpbmF0aW9uOiBQYWdpbmF0aW9uCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVMaXN0OiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHRvdGFsOiAwLAogICAgICBsaXN0UXVlcnk6IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBjb250YWluZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgYXBwbGljYXRpb25JZDogdW5kZWZpbmVkLAogICAgICAgIGRldmljZVJlc291cmNlVHlwZTogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIGxvZ3NEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY29udGFpbmVyTG9nczogJycsCiAgICAgIGN1cnJlbnRDb250YWluZXI6IG51bGwsCiAgICAgIGRlcGxveURpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkZXBsb3lGb3JtOiB7CiAgICAgICAgY29udGFpbmVyTmFtZTogJycsCiAgICAgICAgaW1hZ2VJZDogbnVsbCwKICAgICAgICBpbWFnZU5hbWU6ICcnLAogICAgICAgIGltYWdlVGFnOiAnbGF0ZXN0JywKICAgICAgICBzZXJ2aWNlUG9ydDogODAsCiAgICAgICAgcmVwbGljYXM6IDEsCiAgICAgICAgZGlzdHJpYnV0aW9uU3RyYXRlZ3k6ICdTUFJFQURfQUNST1NTX05PREVTJywKICAgICAgICBlbmFibGVIc206IGZhbHNlLAogICAgICAgIGhzbURldmljZUlkOiBudWxsLAogICAgICAgIC8vIOaWsOeahOiuvuWkh+i1hOa6kOmFjee9rgogICAgICAgIGVuYWJsZURldmljZVJlc291cmNlOiBmYWxzZSwKICAgICAgICBkZXZpY2VSZXNvdXJjZVR5cGU6ICcnLAogICAgICAgIGRldmljZVJlc291cmNlSWRzOiBbXSwKICAgICAgICBhbGxvY2F0aW9uVHlwZTogJ2V4Y2x1c2l2ZScsCiAgICAgICAgZGVwbG95ZWRCeTogbnVsbCwKICAgICAgICBhcHBsaWNhdGlvbklkOiBudWxsLAogICAgICAgIGFwcGxpY2F0aW9uTmFtZTogJycKICAgICAgfSwKICAgICAgZGVwbG95UnVsZXM6IHsKICAgICAgICBhcHBsaWNhdGlvbklkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup5YWz6IGU5bqU55SoJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0sCiAgICAgICAgY29udGFpbmVyTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeWuueWZqOWQjeensCcsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBpbWFnZUlkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup6ZWc5YOPJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0sCiAgICAgICAgc2VydmljZVBvcnQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXmnI3liqHnq6/lj6MnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgc2NhbGVEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc2NhbGVGb3JtOiB7CiAgICAgICAgaWQ6ICcnLAogICAgICAgIHJlcGxpY2FzOiAxLAogICAgICAgIGRpc3RyaWJ1dGlvblN0cmF0ZWd5OiAnU1BSRUFEX0FDUk9TU19OT0RFUycKICAgICAgfSwKICAgICAgc2NhbGVSdWxlczogewogICAgICAgIHJlcGxpY2FzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5Ymv5pys5pWw6YePJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOmVnOWDj+ebuOWFs+aVsOaNrgogICAgICBpbWFnZUxpc3Q6IFtdLAogICAgICBpbWFnZUxpc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRJbWFnZTogbnVsbCwKICAgICAgLy8gSFNN6K6+5aSH55u45YWz5pWw5o2uCiAgICAgIGhzbURldmljZUxpc3Q6IFtdLAogICAgICBoc21EZXZpY2VMaXN0TG9hZGluZzogZmFsc2UsCiAgICAgIHNlbGVjdGVkSHNtRGV2aWNlOiBudWxsLAogICAgICAvLyDorr7lpIfotYTmupDnm7jlhbPmlbDmja4KICAgICAgYXZhaWxhYmxlRGV2aWNlUmVzb3VyY2VzOiBbXSwKICAgICAgZGV2aWNlUmVzb3VyY2VMaXN0TG9hZGluZzogZmFsc2UsCiAgICAgIHNlbGVjdGVkRGV2aWNlUmVzb3VyY2VzOiBbXSwKICAgICAgLy8g5bqU55So5LiL5ouJCiAgICAgIGFwcE9wdGlvbnM6IFtdLAogICAgICBhcHBPcHRpb25zTG9hZGluZzogZmFsc2UKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlUmVzZXRTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0U2VhcmNoKCkgewogICAgICB0aGlzLmxpc3RRdWVyeSA9IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBjb250YWluZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlRGVwbG95Q29udGFpbmVyOiBmdW5jdGlvbiBoYW5kbGVEZXBsb3lDb250YWluZXIoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIC8vIOmmluWFiOWKoOi9vemVnOWDj+WIl+ihqAogICAgICBQcm9taXNlLmFsbChbdGhpcy5sb2FkSW1hZ2VMaXN0KCksIHRoaXMubG9hZEFwcE9wdGlvbnMoKV0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmRlcGxveUZvcm0gPSB7CiAgICAgICAgICBjb250YWluZXJOYW1lOiAnJywKICAgICAgICAgIGltYWdlSWQ6IG51bGwsCiAgICAgICAgICBpbWFnZU5hbWU6ICcnLAogICAgICAgICAgaW1hZ2VUYWc6ICdsYXRlc3QnLAogICAgICAgICAgcmVwbGljYXM6IDEsCiAgICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnLAogICAgICAgICAgZW5hYmxlSHNtOiBmYWxzZSwKICAgICAgICAgIGhzbURldmljZUlkOiBudWxsLAogICAgICAgICAgLy8g6YeN572u5paw55qE6K6+5aSH6LWE5rqQ6YWN572uCiAgICAgICAgICBlbmFibGVEZXZpY2VSZXNvdXJjZTogZmFsc2UsCiAgICAgICAgICBkZXZpY2VSZXNvdXJjZVR5cGU6ICcnLAogICAgICAgICAgZGV2aWNlUmVzb3VyY2VJZHM6IFtdLAogICAgICAgICAgYWxsb2NhdGlvblR5cGU6ICdleGNsdXNpdmUnLAogICAgICAgICAgZGVwbG95ZWRCeTogX3RoaXMudXNlciA/IF90aGlzLnVzZXIuaWQgOiBudWxsLAogICAgICAgICAgYXBwbGljYXRpb25JZDogbnVsbCwKICAgICAgICAgIGFwcGxpY2F0aW9uTmFtZTogJycKICAgICAgICB9OwogICAgICAgIF90aGlzLnNlbGVjdGVkSW1hZ2UgPSBudWxsOwogICAgICAgIF90aGlzLnNlbGVjdGVkSHNtRGV2aWNlID0gbnVsbDsKICAgICAgICBfdGhpcy5zZWxlY3RlZERldmljZVJlc291cmNlcyA9IFtdOwogICAgICAgIF90aGlzLmF2YWlsYWJsZURldmljZVJlc291cmNlcyA9IFtdOwogICAgICAgIF90aGlzLmRlcGxveURpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICAgIF90aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpcy4kcmVmc1snZGVwbG95Rm9ybSddLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgY29uZmlybURlcGxveUNvbnRhaW5lcjogZnVuY3Rpb24gY29uZmlybURlcGxveUNvbnRhaW5lcigpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbJ2RlcGxveUZvcm0nXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8vIOajgOafpUhTTeiuvuWkh+mFjee9rgogICAgICAgICAgaWYgKF90aGlzMi5kZXBsb3lGb3JtLmVuYWJsZUhzbSAmJiAhX3RoaXMyLmRlcGxveUZvcm0uaHNtRGV2aWNlSWQpIHsKICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6lIU03orr7lpIcnKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOajgOafpeiuvuWkh+i1hOa6kOmFjee9rgogICAgICAgICAgaWYgKF90aGlzMi5kZXBsb3lGb3JtLmVuYWJsZURldmljZVJlc291cmNlKSB7CiAgICAgICAgICAgIGlmICghX3RoaXMyLmRlcGxveUZvcm0uZGV2aWNlUmVzb3VyY2VUeXBlKSB7CiAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6norr7lpIfotYTmupDnsbvlnosnKTsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKCFfdGhpczIuZGVwbG95Rm9ybS5kZXZpY2VSZXNvdXJjZUlkcyB8fCBfdGhpczIuZGVwbG95Rm9ybS5kZXZpY2VSZXNvdXJjZUlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeiuvuWkh+i1hOa6kCcpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgdmFyIGRlcGxveURhdGEgPSBfb2JqZWN0U3ByZWFkKHt9LCBfdGhpczIuZGVwbG95Rm9ybSk7CgogICAgICAgICAgLy8g56Gu5L+d6ZWc5YOP5L+h5oGv5a6M5pW0CiAgICAgICAgICBpZiAoX3RoaXMyLnNlbGVjdGVkSW1hZ2UpIHsKICAgICAgICAgICAgZGVwbG95RGF0YS5pbWFnZU5hbWUgPSBfdGhpczIuc2VsZWN0ZWRJbWFnZS5pbWFnZU5hbWU7CiAgICAgICAgICAgIGRlcGxveURhdGEuaW1hZ2VUYWcgPSBfdGhpczIuc2VsZWN0ZWRJbWFnZS5pbWFnZVRhZzsKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDmnoTlu7pIU03orr7lpIfphY3nva4KICAgICAgICAgIGlmIChfdGhpczIuZGVwbG95Rm9ybS5lbmFibGVIc20gJiYgX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlKSB7CiAgICAgICAgICAgIGRlcGxveURhdGEuaHNtRGV2aWNlQ29uZmlnID0gewogICAgICAgICAgICAgIGVuY3J5cHRvckdyb3VwSWQ6IDEsCiAgICAgICAgICAgICAgLy8g6buY6K6k57uESUQKICAgICAgICAgICAgICBlbmNyeXB0b3JJZDogX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlLmRldmljZUlkLAogICAgICAgICAgICAgIGVuY3J5cHRvck5hbWU6IF90aGlzMi5zZWxlY3RlZEhzbURldmljZS5kZXZpY2VOYW1lLAogICAgICAgICAgICAgIHNlcnZlcklwQWRkcjogX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlLmlwQWRkcmVzcywKICAgICAgICAgICAgICBzZXJ2ZXJQb3J0OiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UubWFuYWdlbWVudFBvcnQsCiAgICAgICAgICAgICAgLy8g5L2/55So566h55CG56uv5Y+jODAxOAogICAgICAgICAgICAgIHRjcENvbm5OdW06IF90aGlzMi5zZWxlY3RlZEhzbURldmljZS50Y3BDb25uTnVtIHx8IDUsCiAgICAgICAgICAgICAgbXNnSGVhZExlbjogX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlLm1zZ0hlYWRMZW4gfHwgNCwKICAgICAgICAgICAgICBtc2dUYWlsTGVuOiBfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UubXNnVGFpbExlbiB8fCAwLAogICAgICAgICAgICAgIGFzY2lpT3JFYmNkaWM6IF90aGlzMi5zZWxlY3RlZEhzbURldmljZS5lbmNvZGluZyB8fCAwLAogICAgICAgICAgICAgIGR5bmFtaWNMaWJQYXRoOiAnLi9saWJkZXZpY2VhcGkuc28nCiAgICAgICAgICAgIH07CiAgICAgICAgICB9CgogICAgICAgICAgLy8g5LiN5YaN5L2/55SoVHJhZWZpa+mFjee9ru+8jOS9v+eUqERvY2tlciBTd2FybeWGhee9rui0n+i9veWdh+ihoQoKICAgICAgICAgIC8vIOiuvue9ruWIhuW4g+etlueVpQogICAgICAgICAgaWYgKGRlcGxveURhdGEucmVwbGljYXMgPiAxICYmIGRlcGxveURhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kpIHsKICAgICAgICAgICAgZGVwbG95RGF0YS5kaXN0cmlidXRpb25TdHJhdGVneSA9IGRlcGxveURhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3k7CiAgICAgICAgICB9CgogICAgICAgICAgLy8g5paw55qE6K6+5aSH6LWE5rqQ6YWN572u77yI5LyY5YWI5L2/55So5paw55qE6YWN572u5pa55byP77yJCiAgICAgICAgICBpZiAoX3RoaXMyLmRlcGxveUZvcm0uZW5hYmxlRGV2aWNlUmVzb3VyY2UgJiYgX3RoaXMyLmRlcGxveUZvcm0uZGV2aWNlUmVzb3VyY2VJZHMubGVuZ3RoID4gMCkgewogICAgICAgICAgICBkZXBsb3lEYXRhLmRldmljZVJlc291cmNlQ29uZmlnID0gewogICAgICAgICAgICAgIGRldmljZVJlc291cmNlVHlwZTogX3RoaXMyLmRlcGxveUZvcm0uZGV2aWNlUmVzb3VyY2VUeXBlLAogICAgICAgICAgICAgIGRldmljZVJlc291cmNlSWRzOiBfdGhpczIuZGVwbG95Rm9ybS5kZXZpY2VSZXNvdXJjZUlkcywKICAgICAgICAgICAgICBhbGxvY2F0aW9uVHlwZTogX3RoaXMyLmRlcGxveUZvcm0uYWxsb2NhdGlvblR5cGUsCiAgICAgICAgICAgICAgcHJpb3JpdHk6IDEsCiAgICAgICAgICAgICAgY29uZmlnRGF0YTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICAgICAgZGV2aWNlVHlwZTogX3RoaXMyLmRlcGxveUZvcm0uZGV2aWNlUmVzb3VyY2VUeXBlLAogICAgICAgICAgICAgICAgYWxsb2NhdGlvblR5cGU6IF90aGlzMi5kZXBsb3lGb3JtLmFsbG9jYXRpb25UeXBlLAogICAgICAgICAgICAgICAgc2VsZWN0ZWREZXZpY2VzOiBfdGhpczIuc2VsZWN0ZWREZXZpY2VSZXNvdXJjZXMubWFwKGZ1bmN0aW9uIChkZXZpY2UpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICBpZDogZGV2aWNlLmlkLAogICAgICAgICAgICAgICAgICAgIG5hbWU6IGRldmljZS5uYW1lLAogICAgICAgICAgICAgICAgICAgIHR5cGU6IGRldmljZS50eXBlLAogICAgICAgICAgICAgICAgICAgIGlwQWRkcmVzczogZGV2aWNlLmlwQWRkcmVzcywKICAgICAgICAgICAgICAgICAgICBwb3J0OiBkZXZpY2UucG9ydAogICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9OwogICAgICAgICAgfQogICAgICAgICAgLy8g5YW85a655pen55qESFNN6K6+5aSH6YWN572uCiAgICAgICAgICBlbHNlIGlmIChfdGhpczIuZGVwbG95Rm9ybS5lbmFibGVIc20gJiYgX3RoaXMyLnNlbGVjdGVkSHNtRGV2aWNlICYmIGRlcGxveURhdGEuaHNtRGV2aWNlQ29uZmlnKSB7CiAgICAgICAgICAgIGRlcGxveURhdGEuZGV2aWNlUmVzb3VyY2VDb25maWcgPSB7CiAgICAgICAgICAgICAgZGV2aWNlUmVzb3VyY2VUeXBlOiAnVlNNJywKICAgICAgICAgICAgICBkZXZpY2VSZXNvdXJjZUlkczogW190aGlzMi5zZWxlY3RlZEhzbURldmljZS5kZXZpY2VJZF0sCiAgICAgICAgICAgICAgYWxsb2NhdGlvblR5cGU6ICdleGNsdXNpdmUnLAogICAgICAgICAgICAgIHByaW9yaXR5OiAxLAogICAgICAgICAgICAgIGNvbmZpZ0RhdGE6IEpTT04uc3RyaW5naWZ5KGRlcGxveURhdGEuaHNtRGV2aWNlQ29uZmlnKQogICAgICAgICAgICB9OwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOS9v+eUqOe7n+S4gOeahOmDqOe9suaOpeWPowogICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqOe7n+S4gOeahOmDqOe9suaOpeWPo++8jOmFjee9ru+8micsIHsKICAgICAgICAgICAgaHNtOiBfdGhpczIuZGVwbG95Rm9ybS5lbmFibGVIc20sCiAgICAgICAgICAgIHN0cmF0ZWd5OiBkZXBsb3lEYXRhLmRpc3RyaWJ1dGlvblN0cmF0ZWd5LAogICAgICAgICAgICByZXBsaWNhczogZGVwbG95RGF0YS5yZXBsaWNhcwogICAgICAgICAgfSk7CiAgICAgICAgICBkZXBsb3lDb250YWluZXIoZGVwbG95RGF0YSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXMyLmRlcGxveURpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuS4jeWQjOeahOWTjeW6lOe7k+aehAogICAgICAgICAgICAgICAgdmFyIG1lc3NhZ2UgPSAn5a655Zmo6YOo572y5oiQ5Yqf77yBJzsKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmFjY2Vzc1VybCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9ICJcdThCQkZcdTk1RUVcdTU3MzBcdTU3NDBcdUZGMUEiLmNvbmNhdChyZXNwb25zZS5kYXRhLmFjY2Vzc1VybCk7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEuY29udGFpbmVyICYmIHJlc3BvbnNlLmRhdGEuY29udGFpbmVyLmFjY2Vzc1VybCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9ICJcdThCQkZcdTk1RUVcdTU3MzBcdTU3NDBcdUZGMUEiLmNvbmNhdChyZXNwb25zZS5kYXRhLmNvbnRhaW5lci5hY2Nlc3NVcmwpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZGlzdHJpYnV0aW9uU3RyYXRlZ3kpIHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZSArPSAiXHVGRjBDXHU1MjA2XHU1RTAzXHU3QjU2XHU3NTY1XHVGRjFBIi5jb25jYXQoX3RoaXMyLmdldFN0cmF0ZWd5RGlzcGxheU5hbWUocmVzcG9uc2UuZGF0YS5kaXN0cmlidXRpb25TdHJhdGVneSkpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuaHNtQ29uZmlndXJlZCkgewogICAgICAgICAgICAgICAgICBtZXNzYWdlICs9ICJcdUZGMENIU01cdThCQkVcdTU5MDdcdUZGMUEiLmNvbmNhdChfdGhpczIuc2VsZWN0ZWRIc21EZXZpY2UuZGV2aWNlTmFtZSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2Uuc3VjY2VzcyhtZXNzYWdlKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOmDqOe9suS7u+WKoeW3suaPkOS6pCcpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczIuZ2V0TGlzdCgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfpg6jnvbLlpLHotKUnKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgICAvLyDplJnor6/lpITnkIblt7LlnKjmi6bmiKrlmajkuK3lpITnkIYKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yqg6L295bqU55So6YCJ6aG5CiAgICBsb2FkQXBwT3B0aW9uczogZnVuY3Rpb24gbG9hZEFwcE9wdGlvbnMoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvcigpLm0oZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgcmVzcCwgZGF0YSwgX3Q7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfdGhpczMuYXBwT3B0aW9uc0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0LnAgPSAxOwogICAgICAgICAgICAgIGlmICghKF90aGlzMy51c2VyICYmIEFycmF5LmlzQXJyYXkoX3RoaXMzLnVzZXIucm9sZXMpICYmIF90aGlzMy51c2VyLnJvbGVzLmluY2x1ZGVzKCdST0xFX1RFTkFOVCcpKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDM7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIGZldGNoVXNlckFwcHMoKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJlc3AgPSBfY29udGV4dC52OwogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSA1OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIGZldGNoQXBwbGljYXRpb25PcHRpb25zKCk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXNwID0gX2NvbnRleHQudjsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIGlmIChyZXNwICYmIHJlc3AuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgICAgICAgIGRhdGEgPSBBcnJheS5pc0FycmF5KHJlc3AuZGF0YSkgPyByZXNwLmRhdGEgOiByZXNwLmRhdGEgJiYgcmVzcC5kYXRhLmxpc3QgPyByZXNwLmRhdGEubGlzdCA6IFtdOwogICAgICAgICAgICAgICAgX3RoaXMzLmFwcE9wdGlvbnMgPSBkYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICBpZiAoaXRlbS5sYWJlbCAmJiBpdGVtLnZhbHVlICE9PSB1bmRlZmluZWQpIHJldHVybiBpdGVtOwogICAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICAgIGxhYmVsOiBpdGVtLm5hbWUgfHwgaXRlbS5hcHBsaWNhdGlvbk5hbWUgfHwgaXRlbS5sYWJlbCwKICAgICAgICAgICAgICAgICAgICB2YWx1ZTogU3RyaW5nKGl0ZW0uaWQgfHwgaXRlbS52YWx1ZSkKICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczMuYXBwT3B0aW9ucyA9IFtdOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dC5uID0gNzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF9jb250ZXh0LnAgPSA2OwogICAgICAgICAgICAgIF90ID0gX2NvbnRleHQudjsKICAgICAgICAgICAgICBfdGhpczMuYXBwT3B0aW9ucyA9IFtdOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQucCA9IDc7CiAgICAgICAgICAgICAgX3RoaXMzLmFwcE9wdGlvbnNMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmYoNyk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzEsIDYsIDcsIDhdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZVN0YXJ0Q29udGFpbmVyOiBmdW5jdGlvbiBoYW5kbGVTdGFydENvbnRhaW5lcihyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHN0YXJ0Q29udGFpbmVyKHJvdy5pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOWQr+WKqOaIkOWKnycpOwogICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVN0b3BDb250YWluZXI6IGZ1bmN0aW9uIGhhbmRsZVN0b3BDb250YWluZXIocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBzdG9wQ29udGFpbmVyKHJvdy5pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOWBnOatouaIkOWKnycpOwogICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJlc3RhcnRDb250YWluZXI6IGZ1bmN0aW9uIGhhbmRsZVJlc3RhcnRDb250YWluZXIocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICByZXN0YXJ0Q29udGFpbmVyKHJvdy5pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM2LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuueWZqOmHjeWQr+aIkOWKnycpOwogICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJlbW92ZUNvbnRhaW5lcjogZnVuY3Rpb24gaGFuZGxlUmVtb3ZlQ29udGFpbmVyKHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGhpcy4kbXNnYm94LmNvbmZpcm0oIlx1Nzg2RVx1OEJBNFx1ODk4MVx1NTIyMFx1OTY2NFx1NUJCOVx1NTY2OCAiLmNvbmNhdChyb3cuY29udGFpbmVyTmFtZSwgIiBcdTU0MTdcdUZGMUYiKSwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJlbW92ZUNvbnRhaW5lcihyb3cuaWQpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM3LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwogICAgICAgICAgX3RoaXM3LmdldExpc3QoKTsKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOWPlua2iOWIoOmZpAogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTY2FsZUNvbnRhaW5lcjogZnVuY3Rpb24gaGFuZGxlU2NhbGVDb250YWluZXIocm93KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB0aGlzLnNjYWxlRm9ybSA9IHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHJlcGxpY2FzOiByb3cucmVwbGljYXMgfHwgMSwKICAgICAgICAvLyDkvb/nlKjlvZPliY3lia/mnKzmlbDmiJbpu5jorqTlgLwKICAgICAgICBkaXN0cmlidXRpb25TdHJhdGVneTogcm93LmRpc3RyaWJ1dGlvblN0cmF0ZWd5IHx8ICdTUFJFQURfQUNST1NTX05PREVTJwogICAgICB9OwogICAgICB0aGlzLnNjYWxlRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczguJHJlZnNbJ3NjYWxlRm9ybSddLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICB9LAogICAgY29uZmlybVNjYWxlQ29udGFpbmVyOiBmdW5jdGlvbiBjb25maXJtU2NhbGVDb250YWluZXIoKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydzY2FsZUZvcm0nXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNjYWxlQ29udGFpbmVyKF90aGlzOS5zY2FsZUZvcm0uaWQsIF90aGlzOS5zY2FsZUZvcm0ucmVwbGljYXMpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczkuc2NhbGVEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzOS4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajmiannvKnlrrnku7vliqHlt7Lmj5DkuqQnKTsKICAgICAgICAgICAgX3RoaXM5LmdldExpc3QoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVmlld0xvZ3M6IGZ1bmN0aW9uIGhhbmRsZVZpZXdMb2dzKHJvdykgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50Q29udGFpbmVyID0gcm93OwogICAgICBnZXRDb250YWluZXJMb2dzKHJvdy5pZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgIF90aGlzMC5jb250YWluZXJMb2dzID0gcmVzcG9uc2UuZGF0YSB8fCAnJzsKICAgICAgICAgIF90aGlzMC5sb2dzRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTeW5jU3RhdHVzOiBmdW5jdGlvbiBoYW5kbGVTeW5jU3RhdHVzKCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgc3luY0NvbnRhaW5lclN0YXR1cygpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMS4kbWVzc2FnZS5zdWNjZXNzKCflrrnlmajnirbmgIHlkIzmraXku7vliqHlt7Lmj5DkuqQnKTsKICAgICAgICBfdGhpczEuZ2V0TGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldENvbnRhaW5lckxpc3QodGhpcy5saXN0UXVlcnkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICBfdGhpczEwLnRhYmxlTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdCB8fCBbXTsKICAgICAgICAgIF90aGlzMTAudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsQ291bnQgfHwgMDsKCiAgICAgICAgICAvLyDkuLrmr4/kuKrlrrnlmajliqDovb3orr7lpIfotYTmupDkv6Hmga8KICAgICAgICAgIF90aGlzMTAubG9hZENvbnRhaW5lckRldmljZVJlc291cmNlcygpOwogICAgICAgIH0KICAgICAgICBfdGhpczEwLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMTAubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDliqDovb3lrrnlmajorr7lpIfotYTmupDkv6Hmga8KICAgIGxvYWRDb250YWluZXJEZXZpY2VSZXNvdXJjZXM6IGZ1bmN0aW9uIGxvYWRDb250YWluZXJEZXZpY2VSZXNvdXJjZXMoKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciBwcm9taXNlczsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgaWYgKCEoIV90aGlzMTEudGFibGVMaXN0IHx8IF90aGlzMTEudGFibGVMaXN0Lmxlbmd0aCA9PT0gMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmEoMik7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICAvLyDlubblj5HliqDovb3miYDmnInlrrnlmajnmoTorr7lpIfotYTmupDkv6Hmga8KICAgICAgICAgICAgICBwcm9taXNlcyA9IF90aGlzMTEudGFibGVMaXN0Lm1hcCgvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgdmFyIF9yZWYgPSBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlMihjb250YWluZXIpIHsKICAgICAgICAgICAgICAgICAgdmFyIHJlc3BvbnNlLCBkZXZpY2VSZXNvdXJjZXMsIF90MjsKICAgICAgICAgICAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0MikgewogICAgICAgICAgICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5uKSB7CiAgICAgICAgICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMDsKICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAxOwogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZ2V0Q29udGFpbmVyRGV2aWNlUmVzb3VyY2VzKGNvbnRhaW5lci5pZCk7CiAgICAgICAgICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQyLnY7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCAmJiByZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5aSE55CG5LiN5ZCM55qE5pWw5o2u57uT5p6ECiAgICAgICAgICAgICAgICAgICAgICAgICAgZGV2aWNlUmVzb3VyY2VzID0gW107CiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOebtOaOpeaVsOe7hOe7k+aehAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGV2aWNlUmVzb3VyY2VzID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEubGlzdCAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEubGlzdCkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWIhumhtee7k+aehO+8mnt0b3RhbENvdW50OiAxLCBsaXN0OiBbLi4uXX0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRldmljZVJlc291cmNlcyA9IHJlc3BvbnNlLmRhdGEubGlzdDsKICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5bCG6K6+5aSH6LWE5rqQ5L+h5oGv5re75Yqg5Yiw5a655Zmo5a+56LGh5LitCiAgICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS4kc2V0KGNvbnRhaW5lciwgJ2RldmljZVJlc291cmNlcycsIGRldmljZVJlc291cmNlcyk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAzOwogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQyLnAgPSAyOwogICAgICAgICAgICAgICAgICAgICAgICBfdDIgPSBfY29udGV4dDIudjsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCJcdTgzQjdcdTUzRDZcdTVCQjlcdTU2NjgiLmNvbmNhdChjb250YWluZXIuaWQsICJcdTc2ODRcdThCQkVcdTU5MDdcdThENDRcdTZFOTBcdTRGRTFcdTYwNkZcdTU5MzFcdThEMjU6IiksIF90Mik7CiAgICAgICAgICAgICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuYSgyKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzAsIDJdXSk7CiAgICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gKF94KSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBfcmVmLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7CiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIH0oKSk7CiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiBQcm9taXNlLmFsbChwcm9taXNlcyk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDmoLzlvI/ljJbnq6/lj6PmmKDlsITmmL7npLoKICAgIGZvcm1hdFBvcnRzOiBmdW5jdGlvbiBmb3JtYXRQb3J0cyhwb3J0TWFwcGluZ3MpIHsKICAgICAgaWYgKCFwb3J0TWFwcGluZ3MpIHJldHVybiAnLSc7CiAgICAgIHRyeSB7CiAgICAgICAgdmFyIHBvcnRzID0gSlNPTi5wYXJzZShwb3J0TWFwcGluZ3MpOwogICAgICAgIGlmIChBcnJheS5pc0FycmF5KHBvcnRzKSAmJiBwb3J0cy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAvLyByZXR1cm4gcG9ydHMubWFwKHAgPT4gYCR7cC5ob3N0UG9ydCB8fCAnJ306JHtwLmNvbnRhaW5lclBvcnR9LyR7cC5wcm90b2NvbCB8fCAndGNwJ31gKS5qb2luKCcsICcpCiAgICAgICAgICByZXR1cm4gcG9ydHMubWFwKGZ1bmN0aW9uIChwKSB7CiAgICAgICAgICAgIHJldHVybiAiIi5jb25jYXQocC5ob3N0UG9ydCB8fCAnJywgIjovIikuY29uY2F0KHAucHJvdG9jb2wgfHwgJ3RjcCcpOwogICAgICAgICAgfSkuam9pbignLCAnKTsKICAgICAgICB9CiAgICAgICAgcmV0dXJuICctJzsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHJldHVybiBwb3J0TWFwcGluZ3M7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/mmL7npLoKICAgIGZvcm1hdERhdGU6IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZVRpbWUpIHsKICAgICAgaWYgKCFkYXRlVGltZSkgcmV0dXJuICctJzsKICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVUaW1lKS50b0xvY2FsZVN0cmluZygnemgtQ04nKTsKICAgIH0sCiAgICAvLyDlpI3liLbliLDliarotLTmnb8KICAgIGNvcHlUb0NsaXBib2FyZDogZnVuY3Rpb24gY29weVRvQ2xpcGJvYXJkKHRleHQpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICBpZiAobmF2aWdhdG9yLmNsaXBib2FyZCkgewogICAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHRleHQpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMxMi4kbWVzc2FnZS5zdWNjZXNzKCflnLDlnYDlt7LlpI3liLbliLDliarotLTmnb8nKTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDpmY3nuqfmlrnmoYgKICAgICAgICB2YXIgdGV4dEFyZWEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCd0ZXh0YXJlYScpOwogICAgICAgIHRleHRBcmVhLnZhbHVlID0gdGV4dDsKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRleHRBcmVhKTsKICAgICAgICB0ZXh0QXJlYS5zZWxlY3QoKTsKICAgICAgICBkb2N1bWVudC5leGVjQ29tbWFuZCgnY29weScpOwogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGV4dEFyZWEpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zyw5Z2A5bey5aSN5Yi25Yiw5Ymq6LS05p2/Jyk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDphY3nva7orr/pl67ot6/nlLEKICAgIGhhbmRsZUNvbmZpZ1JvdXRlOiBmdW5jdGlvbiBoYW5kbGVDb25maWdSb3V0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMTMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvcigpLm0oZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCBfcmVzcG9uc2UkZGF0YSwgYWNjZXNzUG9ydCwgYWNjZXNzSW5mbywgX3QzOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDQucCA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiBnZXRDb250YWluZXJBY2Nlc3NVcmwocm93LmlkKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ0LnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwICYmIHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgICAgIF9yZXNwb25zZSRkYXRhID0gcmVzcG9uc2UuZGF0YSwgYWNjZXNzUG9ydCA9IF9yZXNwb25zZSRkYXRhLmFjY2Vzc1BvcnQsIGFjY2Vzc0luZm8gPSBfcmVzcG9uc2UkZGF0YS5hY2Nlc3NJbmZvOwogICAgICAgICAgICAgICAgaWYgKGFjY2Vzc1BvcnQgJiYgYWNjZXNzUG9ydCA+IDApIHsKICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5bey5pyJ5YiG6YWN55qE56uv5Y+j77yM5pi+56S66K6/6Zeu5L+h5oGvCiAgICAgICAgICAgICAgICAgIF90aGlzMTMuJGFsZXJ0KCIiLmNvbmNhdChhY2Nlc3NJbmZvLCAiXG5cblx1NUY1M1x1NTI0RFx1NEY3Rlx1NzUyOERvY2tlciBTd2FybVx1NTE4NVx1N0Y2RVx1OEQxRlx1OEY3RFx1NTc0N1x1ODg2MVx1RkYwQ1x1NjVFMFx1OTcwMFx1OTg5RFx1NTkxNlx1OTE0RFx1N0Y2RVx1OERFRlx1NzUzMVx1MzAwMiIpLCAn5a655Zmo6K6/6Zeu5L+h5oGvJywgewogICAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgICAgICAgICB0eXBlOiAnaW5mbycKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmsqHmnInliIbphY3nq6/lj6PvvIzmj5DnpLrnlKjmiLcKICAgICAgICAgICAgICAgICAgX3RoaXMxMy4kYWxlcnQoJ+ivpeWuueWZqOWwmuacquWIhumFjeWklumDqOiuv+mXruerr+WPo+OAglxuXG7or7fnoa7kv53lrrnlmajpg6jnvbLml7bphY3nva7kuobnq6/lj6PmmKDlsITvvIzmiJbogZTns7vnrqHnkIblkZjliIbphY3nq6/lj6PjgIInLCAn5peg6K6/6Zeu56uv5Y+jJywgewogICAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMTMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWuueWZqOiuv+mXruS/oeaBr+Wksei0pe+8micgKyAocmVzcG9uc2UubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfY29udGV4dDQucCA9IDI7CiAgICAgICAgICAgICAgX3QzID0gX2NvbnRleHQ0LnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a655Zmo6K6/6Zeu5L+h5oGv5aSx6LSlOicsIF90Myk7CiAgICAgICAgICAgICAgX3RoaXMxMy4kbWVzc2FnZS5lcnJvcign6I635Y+W5a655Zmo6K6/6Zeu5L+h5oGv5aSx6LSlJyk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTQsIG51bGwsIFtbMCwgMl1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5pu05paw6Lev55Sx6YWN572uCiAgICBoYW5kbGVVcGRhdGVSb3V0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlUm91dGUocm93KSB7CiAgICAgIHZhciBfdGhpczE0ID0gdGhpczsKICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXmlrDnmoTmnI3liqHnq6/lj6MnLCAn5pu05paw6Lev55SxJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICBpbnB1dFZhbGlkYXRvcjogZnVuY3Rpb24gaW5wdXRWYWxpZGF0b3IodmFsdWUpIHsKICAgICAgICAgIHZhciBwb3J0ID0gcGFyc2VJbnQodmFsdWUpOwogICAgICAgICAgaWYgKCFwb3J0IHx8IHBvcnQgPCAxIHx8IHBvcnQgPiA2NTUzNSkgewogICAgICAgICAgICByZXR1cm4gJ+ivt+i+k+WFpeacieaViOeahOerr+WPo+WPtygxLTY1NTM1KSc7CiAgICAgICAgICB9CiAgICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgICB9CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWYyKSB7CiAgICAgICAgdmFyIHZhbHVlID0gX3JlZjIudmFsdWU7CiAgICAgICAgdmFyIHJvdXRlQ29uZmlnID0gewogICAgICAgICAgY29udGFpbmVySWQ6IHJvdy5pZCwKICAgICAgICAgIHNlcnZpY2VQb3J0OiBwYXJzZUludCh2YWx1ZSkKICAgICAgICB9OwogICAgICAgIF90aGlzMTQudXBkYXRlQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDAgJiYgcmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmFjY2Vzc1VybCkgewogICAgICAgICAgICBfdGhpczE0LiRtZXNzYWdlLnN1Y2Nlc3MoIlx1OERFRlx1NzUzMVx1NjZGNFx1NjVCMFx1NjIxMFx1NTI5Rlx1RkYwMVx1NjVCMFx1NTczMFx1NTc0MFx1RkYxQSIuY29uY2F0KHJlc3BvbnNlLmRhdGEuYWNjZXNzVXJsKSk7CiAgICAgICAgICAgIF90aGlzMTQuZ2V0TGlzdCgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMxNC4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfmm7TmlrDlpLHotKUnKTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAvLyDplJnor6/lpITnkIblt7LlnKjmi6bmiKrlmajkuK3lpITnkIYKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOWPlua2iOaTjeS9nAogICAgICB9KTsKICAgIH0sCiAgICAvLyDphY3nva7lrrnlmajot6/nlLHmlrnms5UKICAgIGNvbmZpZ0NvbnRhaW5lclJvdXRlOiBmdW5jdGlvbiBjb25maWdDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykgewogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvcigpLm0oZnVuY3Rpb24gX2NhbGxlZTUoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0NSkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ1Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDUuYSgyLCBfY29uZmlnQ29udGFpbmVyUm91dGUocm91dGVDb25maWcpKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOabtOaWsOWuueWZqOi3r+eUseaWueazlQogICAgdXBkYXRlQ29udGFpbmVyUm91dGU6IGZ1bmN0aW9uIHVwZGF0ZUNvbnRhaW5lclJvdXRlKHJvdXRlQ29uZmlnKSB7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlNigpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5hKDIsIF91cGRhdGVDb250YWluZXJSb3V0ZShyb3V0ZUNvbmZpZykpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU2KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yqg6L296ZWc5YOP5YiX6KGoCiAgICBsb2FkSW1hZ2VMaXN0OiBmdW5jdGlvbiBsb2FkSW1hZ2VMaXN0KCkgewogICAgICB2YXIgX3RoaXMxNSA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlNygpIHsKICAgICAgICB2YXIgcmVzcG9uc2UsIF90NDsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDcubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMxNS5pbWFnZUxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDcucCA9IDE7CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiBnZXRJbWFnZUxpc3QoKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ3LnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwKSB7CiAgICAgICAgICAgICAgICAvLyDlpITnkIbliIbpobXmlbDmja7nu5PmnoQKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubGlzdCkgewogICAgICAgICAgICAgICAgICAvLyDlpoLmnpzov5Tlm57nmoTmmK/liIbpobXnu5PmnoQKICAgICAgICAgICAgICAgICAgX3RoaXMxNS5pbWFnZUxpc3QgPSByZXNwb25zZS5kYXRhLmxpc3QubWFwKGZ1bmN0aW9uIChpbWFnZSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGltYWdlKSwge30sIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIOehruS/nSBzaXplTWIg5piv5pWw5a2X57G75Z6LCiAgICAgICAgICAgICAgICAgICAgICBzaXplTWI6IHR5cGVvZiBpbWFnZS5zaXplTWIgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoaW1hZ2Uuc2l6ZU1iKSA6IGltYWdlLnNpemVNYgogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgICAgICAgICAvLyDlpoLmnpzov5Tlm57nmoTmmK/nm7TmjqXmlbDnu4QKICAgICAgICAgICAgICAgICAgX3RoaXMxNS5pbWFnZUxpc3QgPSByZXNwb25zZS5kYXRhLm1hcChmdW5jdGlvbiAoaW1hZ2UpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBpbWFnZSksIHt9LCB7CiAgICAgICAgICAgICAgICAgICAgICBzaXplTWI6IHR5cGVvZiBpbWFnZS5zaXplTWIgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoaW1hZ2Uuc2l6ZU1iKSA6IGltYWdlLnNpemVNYgogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTUuaW1hZ2VMaXN0ID0gW107CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoX3RoaXMxNS5pbWFnZUxpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UuaW5mbygn5b2T5YmN5rKh5pyJ5Y+v55So55qE6ZWc5YOP77yM6K+35YWI5p6E5bu65oiW5a+85YWl6ZWc5YOPJyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlumVnOWDj+WIl+ihqOWksei0pe+8micgKyAocmVzcG9uc2UubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpOwogICAgICAgICAgICAgICAgX3RoaXMxNS5pbWFnZUxpc3QgPSBbXTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm4gPSA0OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQ3LnAgPSAzOwogICAgICAgICAgICAgIF90NCA9IF9jb250ZXh0Ny52OwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vemVnOWDj+WIl+ihqOWksei0pTonLCBfdDQpOwogICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vemVnOWDj+WIl+ihqOWksei0pScpOwogICAgICAgICAgICAgIF90aGlzMTUuaW1hZ2VMaXN0ID0gW107CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dDcucCA9IDQ7CiAgICAgICAgICAgICAgX3RoaXMxNS5pbWFnZUxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5mKDQpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU3LCBudWxsLCBbWzEsIDMsIDQsIDVdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOWkhOeQhumVnOWDj+mAieaLqeWPmOWMlgogICAgaGFuZGxlSW1hZ2VDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUltYWdlQ2hhbmdlKGltYWdlSWQpIHsKICAgICAgaWYgKGltYWdlSWQpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkSW1hZ2UgPSB0aGlzLmltYWdlTGlzdC5maW5kKGZ1bmN0aW9uIChpbWcpIHsKICAgICAgICAgIHJldHVybiBpbWcuaWQgPT09IGltYWdlSWQ7CiAgICAgICAgfSk7CiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJbWFnZSkgewogICAgICAgICAgLy8g6Ieq5Yqo5aGr5YWF6ZWc5YOP5ZCN56ew5ZKM5qCH562+CiAgICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VOYW1lID0gdGhpcy5zZWxlY3RlZEltYWdlLm5hbWU7CiAgICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VUYWcgPSB0aGlzLnNlbGVjdGVkSW1hZ2UudGFnOwoKICAgICAgICAgIC8vIOaZuuiDveeUn+aIkOWuueWZqOWQjeensO+8iOWmguaenOW9k+WJjeS4uuepuu+8iQogICAgICAgICAgaWYgKCF0aGlzLmRlcGxveUZvcm0uY29udGFpbmVyTmFtZSkgewogICAgICAgICAgICB0aGlzLnN1Z2dlc3RDb250YWluZXJOYW1lKHRoaXMuc2VsZWN0ZWRJbWFnZS5uYW1lLCB0aGlzLnNlbGVjdGVkSW1hZ2UudGFnKTsKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDmoLnmja7plZzlg4/nsbvlnovmmbrog73mjqjojZDnq6/lj6MKICAgICAgICAgIHRoaXMuc3VnZ2VzdFNlcnZpY2VQb3J0KHRoaXMuc2VsZWN0ZWRJbWFnZS5uYW1lKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEltYWdlID0gbnVsbDsKICAgICAgICB0aGlzLmRlcGxveUZvcm0uaW1hZ2VOYW1lID0gJyc7CiAgICAgICAgdGhpcy5kZXBsb3lGb3JtLmltYWdlVGFnID0gJ2xhdGVzdCc7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmmbrog73nlJ/miJDlrrnlmajlkI3np7AKICAgIHN1Z2dlc3RDb250YWluZXJOYW1lOiBmdW5jdGlvbiBzdWdnZXN0Q29udGFpbmVyTmFtZShpbWFnZU5hbWUsIGltYWdlVGFnKSB7CiAgICAgIC8vIOenu+mZpOmVnOWDj+WQjeensOS4reeahOeJueauiuWtl+espu+8jOeUn+aIkOeugOa0geeahOWQjeensAogICAgICB2YXIgYmFzZU5hbWUgPSBpbWFnZU5hbWUucmVwbGFjZSgvW15hLXpBLVowLTldL2csICctJykudG9Mb3dlckNhc2UoKTsKCiAgICAgIC8vIOWmguaenOagh+etvuS4jeaYr2xhdGVzdO+8jOWImeWKoOWFpeagh+etvgogICAgICBpZiAoaW1hZ2VUYWcgJiYgaW1hZ2VUYWcgIT09ICdsYXRlc3QnKSB7CiAgICAgICAgYmFzZU5hbWUgKz0gJy0nICsgaW1hZ2VUYWcucmVwbGFjZSgvW15hLXpBLVowLTldL2csICctJyk7CiAgICAgIH0KCiAgICAgIC8vIOa3u+WKoOaXtumXtOaIs+S/neivgeWUr+S4gOaApwogICAgICB2YXIgdGltZXN0YW1wID0gRGF0ZS5ub3coKS50b1N0cmluZygpLnNsaWNlKC02KTsKICAgICAgdGhpcy5kZXBsb3lGb3JtLmNvbnRhaW5lck5hbWUgPSAiIi5jb25jYXQoYmFzZU5hbWUsICItIikuY29uY2F0KHRpbWVzdGFtcCk7CiAgICB9LAogICAgLy8g5qC55o2u6ZWc5YOP57G75Z6L5pm66IO95o6o6I2Q56uv5Y+jCiAgICBzdWdnZXN0U2VydmljZVBvcnQ6IGZ1bmN0aW9uIHN1Z2dlc3RTZXJ2aWNlUG9ydChpbWFnZU5hbWUpIHsKICAgICAgdmFyIHBvcnRNYXAgPSB7CiAgICAgICAgJ25naW54JzogODAsCiAgICAgICAgJ2FwYWNoZSc6IDgwLAogICAgICAgICdodHRwZCc6IDgwLAogICAgICAgICdteXNxbCc6IDMzMDYsCiAgICAgICAgJ21hcmlhZGInOiAzMzA2LAogICAgICAgICdwb3N0Z3Jlcyc6IDU0MzIsCiAgICAgICAgJ3Bvc3RncmVzcWwnOiA1NDMyLAogICAgICAgICdyZWRpcyc6IDYzNzksCiAgICAgICAgJ21vbmdvZGInOiAyNzAxNywKICAgICAgICAnbW9uZ28nOiAyNzAxNywKICAgICAgICAndG9tY2F0JzogODA4MCwKICAgICAgICAnbm9kZSc6IDMwMDAsCiAgICAgICAgJ3NwcmluZyc6IDgwODAsCiAgICAgICAgJ2phdmEnOiA4MDgwCiAgICAgIH07CgogICAgICAvLyDmn6Xmib7ljLnphY3nmoTplZzlg4/nsbvlnosKICAgICAgZm9yICh2YXIgX2kgPSAwLCBfT2JqZWN0JGVudHJpZXMgPSBPYmplY3QuZW50cmllcyhwb3J0TWFwKTsgX2kgPCBfT2JqZWN0JGVudHJpZXMubGVuZ3RoOyBfaSsrKSB7CiAgICAgICAgdmFyIF9PYmplY3QkZW50cmllcyRfaSA9IF9zbGljZWRUb0FycmF5KF9PYmplY3QkZW50cmllc1tfaV0sIDIpLAogICAgICAgICAga2V5ID0gX09iamVjdCRlbnRyaWVzJF9pWzBdLAogICAgICAgICAgcG9ydCA9IF9PYmplY3QkZW50cmllcyRfaVsxXTsKICAgICAgICBpZiAoaW1hZ2VOYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoa2V5KSkgewogICAgICAgICAgdGhpcy5kZXBsb3lGb3JtLnNlcnZpY2VQb3J0ID0gcG9ydDsKICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8vIOagvOW8j+WMlumVnOWDj+Wkp+WwjwogICAgZm9ybWF0SW1hZ2VTaXplOiBmdW5jdGlvbiBmb3JtYXRJbWFnZVNpemUoc2l6ZU1iKSB7CiAgICAgIGlmICghc2l6ZU1iIHx8IHNpemVNYiA9PT0gMCkgcmV0dXJuICctJzsKCiAgICAgIC8vIOehruS/neaYr+aVsOWtl+exu+WeiwogICAgICB2YXIgc2l6ZSA9IHR5cGVvZiBzaXplTWIgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoc2l6ZU1iKSA6IHNpemVNYjsKICAgICAgaWYgKGlzTmFOKHNpemUpKSByZXR1cm4gJy0nOwoKICAgICAgLy8g5aaC5p6c5bey57uP5pivTULljZXkvY3vvIznm7TmjqXmmL7npLoKICAgICAgaWYgKHNpemUgPCAxMDI0KSB7CiAgICAgICAgcmV0dXJuICIiLmNvbmNhdChzaXplLCAiIE1CIik7CiAgICAgIH0KCiAgICAgIC8vIOi9rOaNouS4ukdCCiAgICAgIHZhciBzaXplR2IgPSBzaXplIC8gMTAyNDsKICAgICAgcmV0dXJuICIiLmNvbmNhdChzaXplR2IudG9GaXhlZCgxKSwgIiBHQiIpOwogICAgfSwKICAgIC8vIOiOt+WPluWIhuW4g+etlueVpeaYvuekuuWQjeensAogICAgZ2V0U3RyYXRlZ3lEaXNwbGF5TmFtZTogZnVuY3Rpb24gZ2V0U3RyYXRlZ3lEaXNwbGF5TmFtZShzdHJhdGVneSkgewogICAgICB2YXIgc3RyYXRlZ3lNYXAgPSB7CiAgICAgICAgJ1NQUkVBRF9BQ1JPU1NfTk9ERVMnOiAn6Leo6IqC54K55YiG5pWjJywKICAgICAgICAnV09SS0VSX05PREVTX09OTFknOiAn5LuFV29ya2Vy6IqC54K5JywKICAgICAgICAnTUFOQUdFUl9OT0RFU19PTkxZJzogJ+S7hU1hbmFnZXLoioLngrknLAogICAgICAgICdCQUxBTkNFRCc6ICflubPooaHliIbluIMnLAogICAgICAgICdTUFJFQURfQUNST1NTX1pPTkVTJzogJ+i3qOWPr+eUqOWMuuWIhuaVoycKICAgICAgfTsKICAgICAgcmV0dXJuIHN0cmF0ZWd5TWFwW3N0cmF0ZWd5XSB8fCBzdHJhdGVneTsKICAgIH0sCiAgICAvLyDojrflj5bmjqjojZDnmoTliIbluIPnrZbnlaUKICAgIGdldFJlY29tbWVuZGVkRGlzdHJpYnV0aW9uU3RyYXRlZ3k6IGZ1bmN0aW9uIGdldFJlY29tbWVuZGVkRGlzdHJpYnV0aW9uU3RyYXRlZ3kocmVwbGljYXMpIHsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWU4KCkgewogICAgICAgIHZhciByZXNwb25zZSwgX3Q1OwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDgpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDgucCA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ4Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiBnZXRSZWNvbW1lbmRlZFN0cmF0ZWd5KHJlcGxpY2FzKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ4LnY7CiAgICAgICAgICAgICAgaWYgKCEocmVzcG9uc2UuY29kZSA9PT0gMjAwMDAgJiYgcmVzcG9uc2UuZGF0YSkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0OC5uID0gMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmEoMiwgcmVzcG9uc2UuZGF0YS5yZWNvbW1lbmRlZFN0cmF0ZWd5KTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF9jb250ZXh0OC5uID0gNDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0OC5wID0gMzsKICAgICAgICAgICAgICBfdDUgPSBfY29udGV4dDgudjsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPluaOqOiNkOWIhuW4g+etlueVpeWksei0pTonLCBfdDUpOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5hKDIsICdTUFJFQURfQUNST1NTX05PREVTJyk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTgsIG51bGwsIFtbMCwgM11dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8gPT09IOiuvuWkh+i1hOa6kOebuOWFs+aWueazlSA9PT0KICAgIC8vIOWkhOeQhuiuvuWkh+i1hOa6kOW8gOWFs+WPmOWMlgogICAgaGFuZGxlRGV2aWNlUmVzb3VyY2VUb2dnbGU6IGZ1bmN0aW9uIGhhbmRsZURldmljZVJlc291cmNlVG9nZ2xlKGVuYWJsZWQpIHsKICAgICAgaWYgKGVuYWJsZWQpIHsKICAgICAgICAvLyDlkK/nlKjorr7lpIfotYTmupDml7boh6rliqjpgInmi6lWU03lubbliqDovb3orr7lpIfliJfooagKICAgICAgICB0aGlzLmRlcGxveUZvcm0uZGV2aWNlUmVzb3VyY2VUeXBlID0gJ1ZTTSc7CiAgICAgICAgdGhpcy5kZXBsb3lGb3JtLmRldmljZVJlc291cmNlSWRzID0gW107CiAgICAgICAgdGhpcy5kZXBsb3lGb3JtLmFsbG9jYXRpb25UeXBlID0gJ2V4Y2x1c2l2ZSc7CiAgICAgICAgdGhpcy5zZWxlY3RlZERldmljZVJlc291cmNlcyA9IFtdOwogICAgICAgIHRoaXMuYXZhaWxhYmxlRGV2aWNlUmVzb3VyY2VzID0gW107CiAgICAgICAgLy8g6Ieq5Yqo5Yqg6L29VlNN6K6+5aSH5YiX6KGoCiAgICAgICAgdGhpcy5sb2FkRGV2aWNlUmVzb3VyY2VMaXN0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g56aB55So6K6+5aSH6LWE5rqQ5pe25riF56m65omA5pyJ55u45YWz6YWN572uCiAgICAgICAgdGhpcy5kZXBsb3lGb3JtLmRldmljZVJlc291cmNlVHlwZSA9ICcnOwogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5kZXZpY2VSZXNvdXJjZUlkcyA9IFtdOwogICAgICAgIHRoaXMuc2VsZWN0ZWREZXZpY2VSZXNvdXJjZXMgPSBbXTsKICAgICAgICB0aGlzLmF2YWlsYWJsZURldmljZVJlc291cmNlcyA9IFtdOwogICAgICB9CiAgICB9LAogICAgLy8g5aSE55CG6K6+5aSH57G75Z6L5Y+Y5YyWCiAgICBoYW5kbGVEZXZpY2VUeXBlQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVEZXZpY2VUeXBlQ2hhbmdlKGRldmljZVR5cGUpIHsKICAgICAgaWYgKGRldmljZVR5cGUpIHsKICAgICAgICAvLyDliIfmjaLorr7lpIfnsbvlnovml7bmuIXnqbrlt7LpgInmi6nnmoTorr7lpIcKICAgICAgICB0aGlzLmRlcGxveUZvcm0uZGV2aWNlUmVzb3VyY2VJZHMgPSBbXTsKICAgICAgICB0aGlzLnNlbGVjdGVkRGV2aWNlUmVzb3VyY2VzID0gW107CiAgICAgICAgLy8g5Yqg6L295a+55bqU57G75Z6L55qE6K6+5aSH5YiX6KGoCiAgICAgICAgdGhpcy5sb2FkRGV2aWNlUmVzb3VyY2VMaXN0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5hdmFpbGFibGVEZXZpY2VSZXNvdXJjZXMgPSBbXTsKICAgICAgICB0aGlzLnNlbGVjdGVkRGV2aWNlUmVzb3VyY2VzID0gW107CiAgICAgIH0KICAgIH0sCiAgICAvLyDliqDovb3orr7lpIfotYTmupDliJfooagKICAgIGxvYWREZXZpY2VSZXNvdXJjZUxpc3Q6IGZ1bmN0aW9uIGxvYWREZXZpY2VSZXNvdXJjZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpczE2ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWU5KCkgewogICAgICAgIHZhciByZXNwb25zZSwgZGV2aWNlTGlzdCwgX3Q2OwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDkpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OS5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoX3RoaXMxNi5kZXBsb3lGb3JtLmRldmljZVJlc291cmNlVHlwZSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ5Lm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuYSgyKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF90aGlzMTYuZGV2aWNlUmVzb3VyY2VMaXN0TG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX2NvbnRleHQ5LnAgPSAyOwogICAgICAgICAgICAgIF9jb250ZXh0OS5uID0gMzsKICAgICAgICAgICAgICByZXR1cm4gZ2V0QXZhaWxhYmxlRGV2aWNlUmVzb3VyY2VzKHsKICAgICAgICAgICAgICAgIGRldmljZVJlc291cmNlVHlwZTogX3RoaXMxNi5kZXBsb3lGb3JtLmRldmljZVJlc291cmNlVHlwZSwKICAgICAgICAgICAgICAgIGV4Y2x1ZGVDb250YWluZXJJZDogbnVsbCwKICAgICAgICAgICAgICAgIC8vIOaWsOmDqOe9suaXtuS4jemcgOimgeaOkumZpAogICAgICAgICAgICAgICAgYWxsb2NhdGlvblR5cGU6IF90aGlzMTYuZGVwbG95Rm9ybS5hbGxvY2F0aW9uVHlwZSAvLyDkvKDpgJLnlKjmiLfpgInmi6nnmoTliIbphY3nsbvlnosKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ5LnY7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+iuvuWkh+i1hOa6kEFQSei/lOWbnjonLCByZXNwb25zZSk7IC8vIOiwg+ivleaXpeW/lwoKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwMDApIHsKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuS4jeWQjOeahOaVsOaNrue7k+aehAogICAgICAgICAgICAgICAgZGV2aWNlTGlzdCA9IFtdOwogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmxpc3QpKSB7CiAgICAgICAgICAgICAgICAgIC8vIOWIhumhtee7k+aehO+8mnt0b3RhbENvdW50OiAxMiwgbGlzdDogWy4uLl19CiAgICAgICAgICAgICAgICAgIGRldmljZUxpc3QgPSByZXNwb25zZS5kYXRhLmxpc3Q7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsKICAgICAgICAgICAgICAgICAgLy8g55u05o6l5pWw57uE57uT5p6ECiAgICAgICAgICAgICAgICAgIGRldmljZUxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgZGV2aWNlTGlzdCA9IFtdOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgX3RoaXMxNi5hdmFpbGFibGVEZXZpY2VSZXNvdXJjZXMgPSBkZXZpY2VMaXN0OwogICAgICAgICAgICAgICAgaWYgKF90aGlzMTYuYXZhaWxhYmxlRGV2aWNlUmVzb3VyY2VzLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICAgICAgICBfdGhpczE2LiRtZXNzYWdlLmluZm8oIlx1NUY1M1x1NTI0RFx1NkNBMVx1NjcwOVx1NTNFRlx1NzUyOFx1NzY4NCIuY29uY2F0KF90aGlzMTYuZGVwbG95Rm9ybS5kZXZpY2VSZXNvdXJjZVR5cGUsICJcdThCQkVcdTU5MDciKSk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5Yqg6L295Yiw6K6+5aSH6LWE5rqQOicsIF90aGlzMTYuYXZhaWxhYmxlRGV2aWNlUmVzb3VyY2VzLmxlbmd0aCwgJ+S4qicpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczE2LiRtZXNzYWdlLmVycm9yKCfojrflj5borr7lpIfotYTmupDliJfooajlpLHotKXvvJonICsgKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKTsKICAgICAgICAgICAgICAgIF90aGlzMTYuYXZhaWxhYmxlRGV2aWNlUmVzb3VyY2VzID0gW107CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0OS5uID0gNTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0OS5wID0gNDsKICAgICAgICAgICAgICBfdDYgPSBfY29udGV4dDkudjsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3orr7lpIfotYTmupDliJfooajlpLHotKU6JywgX3Q2KTsKICAgICAgICAgICAgICBfdGhpczE2LiRtZXNzYWdlLmVycm9yKCfliqDovb3orr7lpIfotYTmupDliJfooajlpLHotKUnKTsKICAgICAgICAgICAgICBfdGhpczE2LmF2YWlsYWJsZURldmljZVJlc291cmNlcyA9IFtdOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgX2NvbnRleHQ5LnAgPSA1OwogICAgICAgICAgICAgIF90aGlzMTYuZGV2aWNlUmVzb3VyY2VMaXN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuZig1KTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOSwgbnVsbCwgW1syLCA0LCA1LCA2XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDlpITnkIborr7lpIfotYTmupDpgInmi6nlj5jljJYKICAgIGhhbmRsZURldmljZVJlc291cmNlQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVEZXZpY2VSZXNvdXJjZUNoYW5nZShkZXZpY2VJZHMpIHsKICAgICAgdmFyIF90aGlzMTcgPSB0aGlzOwogICAgICB0aGlzLnNlbGVjdGVkRGV2aWNlUmVzb3VyY2VzID0gW107CiAgICAgIGlmIChkZXZpY2VJZHMgJiYgZGV2aWNlSWRzLmxlbmd0aCA+IDApIHsKICAgICAgICBkZXZpY2VJZHMuZm9yRWFjaChmdW5jdGlvbiAoZGV2aWNlSWQpIHsKICAgICAgICAgIHZhciBkZXZpY2UgPSBfdGhpczE3LmF2YWlsYWJsZURldmljZVJlc291cmNlcy5maW5kKGZ1bmN0aW9uIChkKSB7CiAgICAgICAgICAgIHJldHVybiBkLmlkID09PSBkZXZpY2VJZDsKICAgICAgICAgIH0pOwogICAgICAgICAgaWYgKGRldmljZSkgewogICAgICAgICAgICBfdGhpczE3LnNlbGVjdGVkRGV2aWNlUmVzb3VyY2VzLnB1c2goZGV2aWNlKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8vIOiOt+WPluiuvuWkh+exu+Wei+minOiJsgogICAgZ2V0RGV2aWNlVHlwZUNvbG9yOiBmdW5jdGlvbiBnZXREZXZpY2VUeXBlQ29sb3IoZGV2aWNlVHlwZSkgewogICAgICB2YXIgY29sb3JNYXAgPSB7CiAgICAgICAgJ1ZTTSc6ICdzdWNjZXNzJywKICAgICAgICAnQ0hTTSc6ICdwcmltYXJ5JywKICAgICAgICAnSFNNJzogJ3dhcm5pbmcnCiAgICAgIH07CiAgICAgIHJldHVybiBjb2xvck1hcFtkZXZpY2VUeXBlXSB8fCAnaW5mbyc7CiAgICB9LAogICAgLy8gPT09IEhTTeiuvuWkh+ebuOWFs+aWueazlSA9PT0KICAgIC8vIOWkhOeQhkhTTeW8gOWFs+WPmOWMlgogICAgaGFuZGxlSHNtVG9nZ2xlOiBmdW5jdGlvbiBoYW5kbGVIc21Ub2dnbGUoZW5hYmxlZCkgewogICAgICBpZiAoZW5hYmxlZCkgewogICAgICAgIC8vIOWQr+eUqEhTTeaXtuWKoOi9veiuvuWkh+WIl+ihqAogICAgICAgIHRoaXMubG9hZEhzbURldmljZUxpc3QoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDnpoHnlKhIU03ml7bmuIXnqbrpgInmi6kKICAgICAgICB0aGlzLmRlcGxveUZvcm0uaHNtRGV2aWNlSWQgPSBudWxsOwogICAgICAgIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UgPSBudWxsOwogICAgICB9CiAgICB9LAogICAgLy8g5Yqg6L29SFNN6K6+5aSH5YiX6KGoCiAgICBsb2FkSHNtRGV2aWNlTGlzdDogZnVuY3Rpb24gbG9hZEhzbURldmljZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpczE4ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWUwKCkgewogICAgICAgIHZhciByZXNwb25zZSwgX3Q3OwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDApIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0MC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfdGhpczE4LmhzbURldmljZUxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDAucCA9IDE7CiAgICAgICAgICAgICAgX2NvbnRleHQwLm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiBnZXRBdmFpbGFibGVIc21EZXZpY2VzKHsKICAgICAgICAgICAgICAgIHN0YXR1czogJ3J1bm5pbmcnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0MC52OwogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAwMCkgewogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5saXN0KSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTguaHNtRGV2aWNlTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdDsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgICAgICAgICBfdGhpczE4LmhzbURldmljZUxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxOC5oc21EZXZpY2VMaXN0ID0gW107CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoX3RoaXMxOC5oc21EZXZpY2VMaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICAgICAgICBfdGhpczE4LiRtZXNzYWdlLmluZm8oJ+W9k+WJjeayoeacieWPr+eUqOeahEhTTeiuvuWkhycpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczE4LiRtZXNzYWdlLmVycm9yKCfojrflj5ZIU03orr7lpIfliJfooajlpLHotKXvvJonICsgKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKTsKICAgICAgICAgICAgICAgIF90aGlzMTguaHNtRGV2aWNlTGlzdCA9IFtdOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDAubiA9IDQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfY29udGV4dDAucCA9IDM7CiAgICAgICAgICAgICAgX3Q3ID0gX2NvbnRleHQwLnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L29SFNN6K6+5aSH5YiX6KGo5aSx6LSlOicsIF90Nyk7CiAgICAgICAgICAgICAgX3RoaXMxOC4kbWVzc2FnZS5lcnJvcign5Yqg6L29SFNN6K6+5aSH5YiX6KGo5aSx6LSlJyk7CiAgICAgICAgICAgICAgX3RoaXMxOC5oc21EZXZpY2VMaXN0ID0gW107CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dDAucCA9IDQ7CiAgICAgICAgICAgICAgX3RoaXMxOC5oc21EZXZpY2VMaXN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDAuZig0KTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDAuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMCwgbnVsbCwgW1sxLCAzLCA0LCA1XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDlpITnkIZIU03orr7lpIfpgInmi6nlj5jljJYKICAgIGhhbmRsZUhzbURldmljZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlSHNtRGV2aWNlQ2hhbmdlKGRldmljZUlkKSB7CiAgICAgIGlmIChkZXZpY2VJZCkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRIc21EZXZpY2UgPSB0aGlzLmhzbURldmljZUxpc3QuZmluZChmdW5jdGlvbiAoZGV2aWNlKSB7CiAgICAgICAgICByZXR1cm4gZGV2aWNlLmRldmljZUlkID09PSBkZXZpY2VJZDsKICAgICAgICB9KTsKICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEhzbURldmljZSkgewogICAgICAgICAgLy8g5Y+v5Lul5Zyo6L+Z6YeM5Yqg6L295pu06K+m57uG55qE6K6+5aSH5L+h5oGvCiAgICAgICAgICB0aGlzLmxvYWRIc21EZXZpY2VEZXRhaWwoZGV2aWNlSWQpOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNlbGVjdGVkSHNtRGV2aWNlID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8vIOWKoOi9vUhTTeiuvuWkh+ivpue7huS/oeaBrwogICAgbG9hZEhzbURldmljZURldGFpbDogZnVuY3Rpb24gbG9hZEhzbURldmljZURldGFpbChkZXZpY2VJZCkgewogICAgICB2YXIgX3RoaXMxOSA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlMSgpIHsKICAgICAgICB2YXIgcmVzcG9uc2UsIF90ODsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yKCkudyhmdW5jdGlvbiAoX2NvbnRleHQxKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDEubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQxLnAgPSAwOwogICAgICAgICAgICAgIF9jb250ZXh0MS5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gZ2V0SHNtRGV2aWNlRGV0YWlsKGRldmljZUlkKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQxLnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMDAwICYmIHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgICAgIF90aGlzMTkuc2VsZWN0ZWRIc21EZXZpY2UgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDEubiA9IDM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfY29udGV4dDEucCA9IDI7CiAgICAgICAgICAgICAgX3Q4ID0gX2NvbnRleHQxLnY7CiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfojrflj5ZIU03orr7lpIfor6bnu4bkv6Hmga/lpLHotKU6JywgX3Q4KTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDEuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMSwgbnVsbCwgW1swLCAyXV0pOwogICAgICB9KSkoKTsKICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIC8vIOmihOWKoOi9vemVnOWDj+WIl+ihqO+8jOaPkOWNh+eUqOaIt+S9k+mqjAogICAgdGhpcy5sb2FkSW1hZ2VMaXN0KCk7CiAgICAvLyDpooTliqDovb1IU03orr7lpIfliJfooagKICAgIHRoaXMubG9hZEhzbURldmljZUxpc3QoKTsKICAgIC8vIOmihOWKoOi9veW6lOeUqOmAiemhuQogICAgdGhpcy5sb2FkQXBwT3B0aW9ucygpOwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoe30sIG1hcEdldHRlcnMoWyd1c2VyJ10pKSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs5Ymv5pys5pWw5Y+Y5YyW77yM6Ieq5Yqo5pu05paw5o6o6I2Q55qE5YiG5biD562W55WlCiAgICAnZGVwbG95Rm9ybS5yZXBsaWNhcyc6IGZ1bmN0aW9uIGRlcGxveUZvcm1SZXBsaWNhcyhuZXdSZXBsaWNhcywgb2xkUmVwbGljYXMpIHsKICAgICAgdmFyIF90aGlzMjAgPSB0aGlzOwogICAgICBpZiAobmV3UmVwbGljYXMgPiAxICYmIG5ld1JlcGxpY2FzICE9PSBvbGRSZXBsaWNhcykgewogICAgICAgIHRoaXMuZ2V0UmVjb21tZW5kZWREaXN0cmlidXRpb25TdHJhdGVneShuZXdSZXBsaWNhcykudGhlbihmdW5jdGlvbiAoc3RyYXRlZ3kpIHsKICAgICAgICAgIF90aGlzMjAuZGVwbG95Rm9ybS5kaXN0cmlidXRpb25TdHJhdGVneSA9IHN0cmF0ZWd5OwogICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKG5ld1JlcGxpY2FzID09PSAxKSB7CiAgICAgICAgLy8g5Y2V5Ymv5pys5pe25LiN6ZyA6KaB5YiG5biD562W55WlCiAgICAgICAgdGhpcy5kZXBsb3lGb3JtLmRpc3RyaWJ1dGlvblN0cmF0ZWd5ID0gJ0JBTEFOQ0VEJzsKICAgICAgfQogICAgfSwKICAgIC8vIOebkeWQrOaJqee8qeWuueWJr+acrOaVsOWPmOWMlgogICAgJ3NjYWxlRm9ybS5yZXBsaWNhcyc6IGZ1bmN0aW9uIHNjYWxlRm9ybVJlcGxpY2FzKG5ld1JlcGxpY2FzLCBvbGRSZXBsaWNhcykgewogICAgICB2YXIgX3RoaXMyMSA9IHRoaXM7CiAgICAgIGlmIChuZXdSZXBsaWNhcyA+IDEgJiYgbmV3UmVwbGljYXMgIT09IG9sZFJlcGxpY2FzKSB7CiAgICAgICAgdGhpcy5nZXRSZWNvbW1lbmRlZERpc3RyaWJ1dGlvblN0cmF0ZWd5KG5ld1JlcGxpY2FzKS50aGVuKGZ1bmN0aW9uIChzdHJhdGVneSkgewogICAgICAgICAgX3RoaXMyMS5zY2FsZUZvcm0uZGlzdHJpYnV0aW9uU3RyYXRlZ3kgPSBzdHJhdGVneTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIGlmIChuZXdSZXBsaWNhcyA9PT0gMSkgewogICAgICAgIHRoaXMuc2NhbGVGb3JtLmRpc3RyaWJ1dGlvblN0cmF0ZWd5ID0gJ0JBTEFOQ0VEJzsKICAgICAgfQogICAgfSwKICAgIC8vIOebkeWQrOWIhumFjeexu+Wei+WPmOWMlu+8jOmHjeaWsOWKoOi9veiuvuWkh+WIl+ihqAogICAgJ2RlcGxveUZvcm0uYWxsb2NhdGlvblR5cGUnOiBmdW5jdGlvbiBkZXBsb3lGb3JtQWxsb2NhdGlvblR5cGUobmV3QWxsb2NhdGlvblR5cGUsIG9sZEFsbG9jYXRpb25UeXBlKSB7CiAgICAgIGlmIChuZXdBbGxvY2F0aW9uVHlwZSAhPT0gb2xkQWxsb2NhdGlvblR5cGUgJiYgdGhpcy5kZXBsb3lGb3JtLmRldmljZVJlc291cmNlVHlwZSkgewogICAgICAgIC8vIOa4heepuuW3sumAieaLqeeahOiuvuWkhwogICAgICAgIHRoaXMuZGVwbG95Rm9ybS5kZXZpY2VSZXNvdXJjZUlkcyA9IFtdOwogICAgICAgIHRoaXMuc2VsZWN0ZWREZXZpY2VSZXNvdXJjZXMgPSBbXTsKICAgICAgICAvLyDph43mlrDliqDovb3orr7lpIfliJfooagKICAgICAgICB0aGlzLmxvYWREZXZpY2VSZXNvdXJjZUxpc3QoKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["mapGetters", "Pagination", "getContainerList", "deployContainer", "getRecommendedStrategy", "startContainer", "stopContainer", "restartContainer", "remove<PERSON><PERSON><PERSON>", "getContainerLogs", "syncContainerStatus", "scaleContainer", "updateContainerImage", "configContainerRoute", "updateContainerRoute", "getAvailableDeviceResources", "getContainerDeviceResources", "getContainerAccessUrl", "getImageList", "fetchApplicationOptions", "fetchUserApps", "getAvailableHsmDevices", "getHsmDeviceDetail", "name", "components", "data", "tableList", "loading", "total", "list<PERSON>uery", "page", "pageSize", "containerName", "undefined", "status", "applicationId", "deviceResourceType", "logsDialogVisible", "containerLogs", "currentC<PERSON><PERSON>", "deployDialogVisible", "deployForm", "imageId", "imageName", "imageTag", "servicePort", "replicas", "distributionStrategy", "enableHsm", "hsmDeviceId", "enableDeviceResource", "deviceResourceIds", "allocationType", "deployedBy", "applicationName", "deployRules", "required", "message", "trigger", "scaleDialogVisible", "scaleForm", "id", "scaleRules", "imageList", "imageListLoading", "selectedImage", "hsmDeviceList", "hsmDeviceListLoading", "selectedHsmDevice", "availableDeviceResources", "deviceResourceListLoading", "selectedDeviceResources", "appOptions", "appOptionsLoading", "methods", "handleSearch", "getList", "handleResetSearch", "handleDeployContainer", "_this", "Promise", "all", "loadImageList", "loadAppOptions", "then", "user", "$nextTick", "$refs", "clearValidate", "confirmDeployContainer", "_this2", "validate", "valid", "$message", "error", "length", "deployData", "_objectSpread", "hsmDeviceConfig", "encryptorGroupId", "encryptorId", "deviceId", "encryptorName", "deviceName", "serverIpAddr", "ip<PERSON><PERSON><PERSON>", "serverPort", "managementPort", "tcpConnNum", "msgHeadLen", "msgTailLen", "asciiOrEbcdic", "encoding", "dynamicLibPath", "deviceResourceConfig", "priority", "configData", "JSON", "stringify", "deviceType", "selectedDevices", "map", "device", "type", "port", "console", "log", "hsm", "strategy", "response", "code", "accessUrl", "concat", "container", "getStrategyDisplayName", "hsmConfigured", "success", "catch", "_this3", "_asyncToGenerator", "_regenerator", "m", "_callee", "resp", "_t", "w", "_context", "n", "p", "Array", "isArray", "roles", "includes", "v", "list", "item", "label", "value", "String", "f", "a", "handleStartContainer", "row", "_this4", "handleStopContainer", "_this5", "handleRestartContainer", "_this6", "handleRemoveContainer", "_this7", "$msgbox", "confirm", "confirmButtonText", "cancelButtonText", "handleScaleContainer", "_this8", "confirmScaleContainer", "_this9", "handleViewLogs", "_this0", "handleSyncStatus", "_this1", "_this10", "totalCount", "loadContainerDeviceResources", "_this11", "_callee3", "promises", "_context3", "_ref", "_callee2", "deviceResources", "_t2", "_context2", "$set", "warn", "_x", "apply", "arguments", "formatPorts", "portMappings", "ports", "parse", "hostPort", "protocol", "join", "e", "formatDate", "dateTime", "Date", "toLocaleString", "copyToClipboard", "text", "_this12", "navigator", "clipboard", "writeText", "textArea", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "handleConfigRoute", "_this13", "_callee4", "_response$data", "accessPort", "accessInfo", "_t3", "_context4", "$alert", "handleUpdateRoute", "_this14", "$prompt", "inputValidator", "parseInt", "_ref2", "routeConfig", "containerId", "_callee5", "_context5", "_callee6", "_context6", "_this15", "_callee7", "_t4", "_context7", "image", "sizeMb", "info", "handleImageChange", "find", "img", "tag", "suggestContainerName", "suggestServicePort", "baseName", "replace", "toLowerCase", "timestamp", "now", "toString", "slice", "portMap", "_i", "_Object$entries", "Object", "entries", "_Object$entries$_i", "_slicedToArray", "key", "formatImageSize", "size", "isNaN", "sizeGb", "toFixed", "strategyMap", "getRecommendedDistributionStrategy", "_callee8", "_t5", "_context8", "recommendedStrategy", "handleDeviceResourceToggle", "enabled", "loadDeviceResourceList", "handleDeviceTypeChange", "_this16", "_callee9", "deviceList", "_t6", "_context9", "excludeContainerId", "handleDeviceResourceChange", "deviceIds", "_this17", "for<PERSON>ach", "d", "push", "getDeviceTypeColor", "colorMap", "handleHsmToggle", "loadHsmDeviceList", "_this18", "_callee0", "_t7", "_context0", "handleHsmDeviceChange", "loadHsmDeviceDetail", "_this19", "_callee1", "_t8", "_context1", "mounted", "computed", "watch", "deployFormReplicas", "newReplicas", "oldReplicas", "_this20", "scaleFormReplicas", "_this21", "deployFormAllocationType", "newAllocationType", "oldAllocationType"], "sources": ["src/views/docker/container/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"所属应用\">\n              <el-select v-model=\"listQuery.applicationId\" clearable placeholder=\"请选择应用\" filterable>\n                <el-option\n                  v-for=\"app in appOptions\"\n                  :key=\"app.value\"\n                  :label=\"app.label\"\n                  :value=\"app.value\"\n                />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"设备类型\">\n              <el-select v-model=\"listQuery.deviceResourceType\" clearable placeholder=\"请选择设备类型\">\n                <el-option label=\"VSM\" value=\"VSM\" />\n                <el-option label=\"CHSM\" value=\"CHSM\" />\n                <el-option label=\"HSM\" value=\"HSM\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"设备资源\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.deviceResources && row.deviceResources.length > 0\">\n              <!-- 如果只有一个设备，显示详细信息 -->\n              <div v-if=\"row.deviceResources.length === 1\" class=\"device-resource-item\">\n                <el-tag :type=\"getDeviceTypeColor(row.deviceResources[0].deviceResourceType)\" size=\"mini\">\n                  {{ row.deviceResources[0].deviceResourceType }}\n                </el-tag>\n                <div class=\"device-info-mini\">\n                  {{ row.deviceResources[0].deviceResourceName || row.deviceResources[0].deviceResourceId }}\n                </div>\n              </div>\n              <!-- 如果有多个设备，显示紧凑格式 -->\n              <div v-else class=\"device-resource-compact\">\n                <div class=\"device-tags\">\n                  <el-tag\n                    v-for=\"device in row.deviceResources\"\n                    :key=\"device.id\"\n                    :type=\"getDeviceTypeColor(device.deviceResourceType)\"\n                    size=\"mini\"\n                    :title=\"`${device.deviceResourceType}: ${device.deviceResourceName || device.deviceResourceId}`\"\n                    style=\"margin: 1px;\"\n                  >\n                    {{ device.deviceResourceType }}\n                  </el-tag>\n                </div>\n                <div class=\"device-count-info\">\n                  共{{ row.deviceResources.length }}个设备\n                </div>\n              </div>\n            </div>\n            <div v-else-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">HSM</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"device-info-mini\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button\n              v-else-if=\"row.status === 'running'\"\n              type=\"text\"\n              size=\"mini\"\n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"部署信息\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <div class=\"deploy-info\">\n              <div v-if=\"row.deployedByName\" class=\"deploy-user\">\n                <i class=\"el-icon-user\"></i> {{ row.deployedByName }}\n              </div>\n              <div v-if=\"row.createTime\" class=\"deploy-time\">\n                <i class=\"el-icon-time\"></i> {{ formatDate(row.createTime) }}\n              </div>\n              <div v-if=\"row.replicas && row.replicas > 1\" class=\"deploy-replicas\">\n                <i class=\"el-icon-copy-document\"></i> 副本: {{ row.replicas }}\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n            <p><strong>服务端口：</strong>8300（Console服务固定端口）</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n\n        <el-form-item label=\"设备资源配置\">\n          <el-switch v-model=\"deployForm.enableDeviceResource\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleDeviceResourceToggle\" />\n          <div class=\"form-help\">启用后可配置各类设备资源（HSM、VSM、CHSM等）</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource\" label=\"设备类型\" prop=\"deviceResourceType\">\n          <el-select\n            v-model=\"deployForm.deviceResourceType\"\n            placeholder=\"请选择设备类型\"\n            @change=\"handleDeviceTypeChange\"\n            style=\"width: 100%;\"\n          >\n            <el-option label=\"VSM (虚拟密码机)\" value=\"VSM\" />\n          </el-select>\n          <div class=\"form-help\">选择要使用的设备资源类型，当前只支持VSM虚拟密码机</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource && deployForm.deviceResourceType\" label=\"分配类型\" prop=\"allocationType\">\n          <el-radio-group v-model=\"deployForm.allocationType\">\n            <el-radio label=\"exclusive\">独占模式</el-radio>\n            <el-radio label=\"shared\">共享模式</el-radio>\n          </el-radio-group>\n          <div class=\"form-help\">\n            独占模式：设备只能被当前容器使用；共享模式：设备可被多个容器共享使用\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource && deployForm.deviceResourceType\" label=\"选择设备\" prop=\"deviceResourceIds\">\n          <el-select\n            v-model=\"deployForm.deviceResourceIds\"\n            multiple\n            filterable\n            :placeholder=\"`请选择${deployForm.deviceResourceType}设备`\"\n            @change=\"handleDeviceResourceChange\"\n            style=\"width: 100%;\"\n            :loading=\"deviceResourceListLoading\"\n            :disabled=\"availableDeviceResources.length === 0\"\n          >\n            <el-option\n              v-for=\"device in availableDeviceResources\"\n              :key=\"device.id\"\n              :label=\"device.name\"\n              :value=\"device.id\"\n              :disabled=\"!device.available\"\n            >\n              <div class=\"device-option\">\n                <div class=\"device-option-main\">\n                  <span class=\"device-name\">{{ device.name }}</span>\n                  <el-tag\n                    :type=\"device.available ? 'success' : 'danger'\"\n                    size=\"mini\"\n                    class=\"device-status\"\n                  >\n                    {{ device.available ? '可用' : '不可用' }}\n                  </el-tag>\n                </div>\n                <div class=\"device-option-detail\">\n                  <span class=\"device-address\">{{ device.ipAddress }}:{{ device.port }}</span>\n                  <span class=\"device-capacity\" v-if=\"device.totalCapacity\">\n                    容量: {{ device.usedCapacity || 0 }}/{{ device.totalCapacity }}\n                  </span>\n                </div>\n              </div>\n            </el-option>\n            <div v-if=\"availableDeviceResources.length === 0\" slot=\"empty\">\n              <span v-if=\"deviceResourceListLoading\">加载中...</span>\n              <span v-else>暂无可用的{{ deployForm.deviceResourceType }}设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的{{ deployForm.deviceResourceType }}设备资源\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              @click=\"loadDeviceResourceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource && selectedDeviceResources.length > 0\" label=\"设备信息\">\n          <div class=\"selected-devices\">\n            <div v-for=\"device in selectedDeviceResources\" :key=\"device.id\" class=\"device-info-card\">\n              <div class=\"device-card-header\">\n                <span class=\"device-card-name\">{{ device.name }}</span>\n                <el-tag :type=\"getDeviceTypeColor(device.type)\" size=\"mini\">{{ device.type }}</el-tag>\n              </div>\n              <div class=\"device-card-content\">\n                <p><strong>地址：</strong>{{ device.ipAddress }}:{{ device.port }}</p>\n                <p v-if=\"device.managementPort\"><strong>管理端口：</strong>{{ device.managementPort }}</p>\n                <p><strong>状态：</strong>\n                  <el-tag v-if=\"device.status === 'normal'\" type=\"success\" size=\"mini\">正常</el-tag>\n                  <el-tag v-else-if=\"device.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n                  <el-tag v-else type=\"warning\" size=\"mini\">{{ device.status }}</el-tag>\n                </p>\n                <p v-if=\"device.totalCapacity\"><strong>容量：</strong>{{ device.usedCapacity || 0 }}/{{ device.totalCapacity }}</p>\n                <p v-if=\"device.description\"><strong>描述：</strong>{{ device.description }}</p>\n              </div>\n            </div>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport {\n  getContainerList,\n  deployContainer,\n  getRecommendedStrategy,\n  startContainer,\n  stopContainer,\n  restartContainer,\n  removeContainer,\n  getContainerLogs,\n  syncContainerStatus,\n  scaleContainer,\n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute,\n  getAvailableDeviceResources,\n  getContainerDeviceResources,\n  getContainerAccessUrl\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined,\n        applicationId: undefined,\n        deviceResourceType: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableHsm: false,\n        hsmDeviceId: null,\n        // 新的设备资源配置\n        enableDeviceResource: false,\n        deviceResourceType: '',\n        deviceResourceIds: [],\n        allocationType: 'exclusive',\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 设备资源相关数据\n      availableDeviceResources: [],\n      deviceResourceListLoading: false,\n      selectedDeviceResources: [],\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableHsm: false,\n          hsmDeviceId: null,\n          // 重置新的设备资源配置\n          enableDeviceResource: false,\n          deviceResourceType: '',\n          deviceResourceIds: [],\n          allocationType: 'exclusive',\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.selectedDeviceResources = []\n        this.availableDeviceResources = []\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n\n          // 检查设备资源配置\n          if (this.deployForm.enableDeviceResource) {\n            if (!this.deployForm.deviceResourceType) {\n              this.$message.error('请选择设备资源类型')\n              return\n            }\n            if (!this.deployForm.deviceResourceIds || this.deployForm.deviceResourceIds.length === 0) {\n              this.$message.error('请选择设备资源')\n              return\n            }\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.managementPort, // 使用管理端口8018\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 不再使用Traefik配置，使用Docker Swarm内置负载均衡\n\n          // 设置分布策略\n          if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            deployData.distributionStrategy = deployData.distributionStrategy\n          }\n\n          // 新的设备资源配置（优先使用新的配置方式）\n          if (this.deployForm.enableDeviceResource && this.deployForm.deviceResourceIds.length > 0) {\n            deployData.deviceResourceConfig = {\n              deviceResourceType: this.deployForm.deviceResourceType,\n              deviceResourceIds: this.deployForm.deviceResourceIds,\n              allocationType: this.deployForm.allocationType,\n              priority: 1,\n              configData: JSON.stringify({\n                deviceType: this.deployForm.deviceResourceType,\n                allocationType: this.deployForm.allocationType,\n                selectedDevices: this.selectedDeviceResources.map(device => ({\n                  id: device.id,\n                  name: device.name,\n                  type: device.type,\n                  ipAddress: device.ipAddress,\n                  port: device.port\n                }))\n              })\n            }\n          }\n          // 兼容旧的HSM设备配置\n          else if (this.deployForm.enableHsm && this.selectedHsmDevice && deployData.hsmDeviceConfig) {\n            deployData.deviceResourceConfig = {\n              deviceResourceType: 'VSM',\n              deviceResourceIds: [this.selectedHsmDevice.deviceId],\n              allocationType: 'exclusive',\n              priority: 1,\n              configData: JSON.stringify(deployData.hsmDeviceConfig)\n            }\n          }\n\n          // 使用统一的部署接口\n          console.log('使用统一的部署接口，配置：', {\n            hsm: this.deployForm.enableHsm,\n            strategy: deployData.distributionStrategy,\n            replicas: deployData.replicas\n          })\n\n          deployContainer(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list || []\n          this.total = response.data.totalCount || 0\n\n          // 为每个容器加载设备资源信息\n          this.loadContainerDeviceResources()\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n\n    // 加载容器设备资源信息\n    async loadContainerDeviceResources() {\n      if (!this.tableList || this.tableList.length === 0) {\n        return\n      }\n\n      // 并发加载所有容器的设备资源信息\n      const promises = this.tableList.map(async (container) => {\n        try {\n          const response = await getContainerDeviceResources(container.id)\n          if (response.code === 20000 && response.data) {\n            // 处理不同的数据结构\n            let deviceResources = []\n            if (Array.isArray(response.data)) {\n              // 直接数组结构\n              deviceResources = response.data\n            } else if (response.data.list && Array.isArray(response.data.list)) {\n              // 分页结构：{totalCount: 1, list: [...]}\n              deviceResources = response.data.list\n            }\n            // 将设备资源信息添加到容器对象中\n            this.$set(container, 'deviceResources', deviceResources)\n          }\n        } catch (error) {\n          console.warn(`获取容器${container.id}的设备资源信息失败:`, error)\n        }\n      })\n\n      await Promise.all(promises)\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          // return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n          return ports.map(p => `${p.hostPort || ''}:/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    async handleConfigRoute(row) {\n      try {\n        // 先获取容器的访问地址信息\n        const response = await getContainerAccessUrl(row.id)\n        if (response.code === 20000 && response.data) {\n          const { accessPort, accessInfo } = response.data\n\n          if (accessPort && accessPort > 0) {\n            // 如果已有分配的端口，显示访问信息\n            this.$alert(\n              `${accessInfo}\\n\\n当前使用Docker Swarm内置负载均衡，无需额外配置路由。`,\n              '容器访问信息',\n              {\n                confirmButtonText: '确定',\n                type: 'info'\n              }\n            )\n          } else {\n            // 如果没有分配端口，提示用户\n            this.$alert(\n              '该容器尚未分配外部访问端口。\\n\\n请确保容器部署时配置了端口映射，或联系管理员分配端口。',\n              '无访问端口',\n              {\n                confirmButtonText: '确定',\n                type: 'warning'\n              }\n            )\n          }\n        } else {\n          this.$message.error('获取容器访问信息失败：' + (response.message || '未知错误'))\n        }\n      } catch (error) {\n        console.error('获取容器访问信息失败:', error)\n        this.$message.error('获取容器访问信息失败')\n      }\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n\n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n\n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n\n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n\n    // === 设备资源相关方法 ===\n\n    // 处理设备资源开关变化\n    handleDeviceResourceToggle(enabled) {\n      if (enabled) {\n        // 启用设备资源时自动选择VSM并加载设备列表\n        this.deployForm.deviceResourceType = 'VSM'\n        this.deployForm.deviceResourceIds = []\n        this.deployForm.allocationType = 'exclusive'\n        this.selectedDeviceResources = []\n        this.availableDeviceResources = []\n        // 自动加载VSM设备列表\n        this.loadDeviceResourceList()\n      } else {\n        // 禁用设备资源时清空所有相关配置\n        this.deployForm.deviceResourceType = ''\n        this.deployForm.deviceResourceIds = []\n        this.selectedDeviceResources = []\n        this.availableDeviceResources = []\n      }\n    },\n\n    // 处理设备类型变化\n    handleDeviceTypeChange(deviceType) {\n      if (deviceType) {\n        // 切换设备类型时清空已选择的设备\n        this.deployForm.deviceResourceIds = []\n        this.selectedDeviceResources = []\n        // 加载对应类型的设备列表\n        this.loadDeviceResourceList()\n      } else {\n        this.availableDeviceResources = []\n        this.selectedDeviceResources = []\n      }\n    },\n\n    // 加载设备资源列表\n    async loadDeviceResourceList() {\n      if (!this.deployForm.deviceResourceType) {\n        return\n      }\n\n      this.deviceResourceListLoading = true\n      try {\n        const response = await getAvailableDeviceResources({\n          deviceResourceType: this.deployForm.deviceResourceType,\n          excludeContainerId: null, // 新部署时不需要排除\n          allocationType: this.deployForm.allocationType // 传递用户选择的分配类型\n        })\n\n        console.log('设备资源API返回:', response) // 调试日志\n\n        if (response.code === 20000) {\n          // 处理不同的数据结构\n          let deviceList = []\n          if (response.data && Array.isArray(response.data.list)) {\n            // 分页结构：{totalCount: 12, list: [...]}\n            deviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            // 直接数组结构\n            deviceList = response.data\n          } else {\n            deviceList = []\n          }\n\n          this.availableDeviceResources = deviceList\n\n          if (this.availableDeviceResources.length === 0) {\n            this.$message.info(`当前没有可用的${this.deployForm.deviceResourceType}设备`)\n          } else {\n            console.log('加载到设备资源:', this.availableDeviceResources.length, '个')\n          }\n        } else {\n          this.$message.error('获取设备资源列表失败：' + (response.message || '未知错误'))\n          this.availableDeviceResources = []\n        }\n      } catch (error) {\n        console.error('加载设备资源列表失败:', error)\n        this.$message.error('加载设备资源列表失败')\n        this.availableDeviceResources = []\n      } finally {\n        this.deviceResourceListLoading = false\n      }\n    },\n\n    // 处理设备资源选择变化\n    handleDeviceResourceChange(deviceIds) {\n      this.selectedDeviceResources = []\n      if (deviceIds && deviceIds.length > 0) {\n        deviceIds.forEach(deviceId => {\n          const device = this.availableDeviceResources.find(d => d.id === deviceId)\n          if (device) {\n            this.selectedDeviceResources.push(device)\n          }\n        })\n      }\n    },\n\n    // 获取设备类型颜色\n    getDeviceTypeColor(deviceType) {\n      const colorMap = {\n        'VSM': 'success',\n        'CHSM': 'primary',\n        'HSM': 'warning'\n      }\n      return colorMap[deviceType] || 'info'\n    },\n\n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n    // 预加载应用选项\n    this.loadAppOptions()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听分配类型变化，重新加载设备列表\n    'deployForm.allocationType'(newAllocationType, oldAllocationType) {\n      if (newAllocationType !== oldAllocationType && this.deployForm.deviceResourceType) {\n        // 清空已选择的设备\n        this.deployForm.deviceResourceIds = []\n        this.selectedDeviceResources = []\n        // 重新加载设备列表\n        this.loadDeviceResourceList()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n\n// 设备资源相关样式\n.device-resource-item {\n  margin-bottom: 4px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.device-info-mini {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.device-option {\n  width: 100%;\n\n  .device-option-main {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 2px;\n\n    .device-name {\n      font-weight: 500;\n    }\n\n    .device-status {\n      margin-left: 8px;\n    }\n  }\n\n  .device-option-detail {\n    display: flex;\n    justify-content: space-between;\n    font-size: 12px;\n    color: #8492a6;\n\n    .device-address {\n      flex: 1;\n    }\n\n    .device-capacity {\n      margin-left: 8px;\n    }\n  }\n}\n\n.selected-devices {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.device-info-card {\n  background-color: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 8px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  .device-card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 8px;\n\n    .device-card-name {\n      font-weight: 600;\n      color: #303133;\n    }\n  }\n\n  .device-card-content {\n    p {\n      margin: 4px 0;\n      font-size: 13px;\n      color: #606266;\n\n      strong {\n        color: #303133;\n      }\n    }\n  }\n}\n\n// 规格信息样式\n.spec-info {\n  font-size: 12px;\n\n  .resource-limit {\n    color: #909399;\n    margin-top: 2px;\n\n    span {\n      margin-right: 8px;\n    }\n  }\n}\n\n// 设备资源显示样式\n.device-resource-compact {\n  .device-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 2px;\n    margin-bottom: 2px;\n  }\n\n  .device-count-info {\n    font-size: 11px;\n    color: #909399;\n    text-align: center;\n  }\n}\n\n// 部署信息样式\n.deploy-info {\n  font-size: 12px;\n\n  .deploy-user,\n  .deploy-time,\n  .deploy-node {\n    display: flex;\n    align-items: center;\n    margin-bottom: 2px;\n    color: #606266;\n\n    i {\n      margin-right: 4px;\n      width: 12px;\n    }\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoZA,SAAAA,UAAA;AACA,OAAAC,UAAA;AACA,SACAC,gBAAA,EACAC,eAAA,EACAC,sBAAA,EACAC,cAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,mBAAA,EACAC,cAAA,EACAC,oBAAA,EACAC,oBAAA,IAAAA,qBAAA,EACAC,oBAAA,IAAAA,qBAAA,EACAC,2BAAA,EACAC,2BAAA,EACAC,qBAAA,QACA;AACA,SAAAC,YAAA;AACA,SAAAC,uBAAA,EAAAC,aAAA;AACA,SAAAC,sBAAA,EAAAC,kBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAvB,UAAA,EAAAA;EACA;EACAwB,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD,SAAA;QACAE,aAAA,EAAAF,SAAA;QACAG,kBAAA,EAAAH;MACA;MACAI,iBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,UAAA;QACAT,aAAA;QACAU,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,QAAA;QACAC,oBAAA;QACAC,SAAA;QACAC,WAAA;QACA;QACAC,oBAAA;QACAd,kBAAA;QACAe,iBAAA;QACAC,cAAA;QACAC,UAAA;QACAlB,aAAA;QACAmB,eAAA;MACA;MACAC,WAAA;QACApB,aAAA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA1B,aAAA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAhB,OAAA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAb,WAAA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,kBAAA;MACAC,SAAA;QACAC,EAAA;QACAf,QAAA;QACAC,oBAAA;MACA;MACAe,UAAA;QACAhB,QAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA;MACAK,SAAA;MACAC,gBAAA;MACAC,aAAA;MACA;MACAC,aAAA;MACAC,oBAAA;MACAC,iBAAA;MACA;MACAC,wBAAA;MACAC,yBAAA;MACAC,uBAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAhD,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA,KAAA2C,OAAA;IACA;IACAE,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACA;MACAC,OAAA,CAAAC,GAAA,OAAAC,aAAA,SAAAC,cAAA,KAAAC,IAAA;QACAL,KAAA,CAAAtC,UAAA;UACAT,aAAA;UACAU,OAAA;UACAC,SAAA;UACAC,QAAA;UACAE,QAAA;UACAC,oBAAA;UACAC,SAAA;UACAC,WAAA;UACA;UACAC,oBAAA;UACAd,kBAAA;UACAe,iBAAA;UACAC,cAAA;UACAC,UAAA,EAAA0B,KAAA,CAAAM,IAAA,GAAAN,KAAA,CAAAM,IAAA,CAAAxB,EAAA;UACA1B,aAAA;UACAmB,eAAA;QACA;QACAyB,KAAA,CAAAd,aAAA;QACAc,KAAA,CAAAX,iBAAA;QACAW,KAAA,CAAAR,uBAAA;QACAQ,KAAA,CAAAV,wBAAA;QACAU,KAAA,CAAAvC,mBAAA;QACAuC,KAAA,CAAAO,SAAA;UACAP,KAAA,CAAAQ,KAAA,eAAAC,aAAA;QACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,eAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,MAAA,CAAAjD,UAAA,CAAAO,SAAA,KAAA0C,MAAA,CAAAjD,UAAA,CAAAQ,WAAA;YACAyC,MAAA,CAAAG,QAAA,CAAAC,KAAA;YACA;UACA;;UAEA;UACA,IAAAJ,MAAA,CAAAjD,UAAA,CAAAS,oBAAA;YACA,KAAAwC,MAAA,CAAAjD,UAAA,CAAAL,kBAAA;cACAsD,MAAA,CAAAG,QAAA,CAAAC,KAAA;cACA;YACA;YACA,KAAAJ,MAAA,CAAAjD,UAAA,CAAAU,iBAAA,IAAAuC,MAAA,CAAAjD,UAAA,CAAAU,iBAAA,CAAA4C,MAAA;cACAL,MAAA,CAAAG,QAAA,CAAAC,KAAA;cACA;YACA;UACA;UAEA,IAAAE,UAAA,GAAAC,aAAA,KAAAP,MAAA,CAAAjD,UAAA;;UAEA;UACA,IAAAiD,MAAA,CAAAzB,aAAA;YACA+B,UAAA,CAAArD,SAAA,GAAA+C,MAAA,CAAAzB,aAAA,CAAAtB,SAAA;YACAqD,UAAA,CAAApD,QAAA,GAAA8C,MAAA,CAAAzB,aAAA,CAAArB,QAAA;UACA;;UAEA;UACA,IAAA8C,MAAA,CAAAjD,UAAA,CAAAO,SAAA,IAAA0C,MAAA,CAAAtB,iBAAA;YACA4B,UAAA,CAAAE,eAAA;cACAC,gBAAA;cAAA;cACAC,WAAA,EAAAV,MAAA,CAAAtB,iBAAA,CAAAiC,QAAA;cACAC,aAAA,EAAAZ,MAAA,CAAAtB,iBAAA,CAAAmC,UAAA;cACAC,YAAA,EAAAd,MAAA,CAAAtB,iBAAA,CAAAqC,SAAA;cACAC,UAAA,EAAAhB,MAAA,CAAAtB,iBAAA,CAAAuC,cAAA;cAAA;cACAC,UAAA,EAAAlB,MAAA,CAAAtB,iBAAA,CAAAwC,UAAA;cACAC,UAAA,EAAAnB,MAAA,CAAAtB,iBAAA,CAAAyC,UAAA;cACAC,UAAA,EAAApB,MAAA,CAAAtB,iBAAA,CAAA0C,UAAA;cACAC,aAAA,EAAArB,MAAA,CAAAtB,iBAAA,CAAA4C,QAAA;cACAC,cAAA;YACA;UACA;;UAEA;;UAEA;UACA,IAAAjB,UAAA,CAAAlD,QAAA,QAAAkD,UAAA,CAAAjD,oBAAA;YACAiD,UAAA,CAAAjD,oBAAA,GAAAiD,UAAA,CAAAjD,oBAAA;UACA;;UAEA;UACA,IAAA2C,MAAA,CAAAjD,UAAA,CAAAS,oBAAA,IAAAwC,MAAA,CAAAjD,UAAA,CAAAU,iBAAA,CAAA4C,MAAA;YACAC,UAAA,CAAAkB,oBAAA;cACA9E,kBAAA,EAAAsD,MAAA,CAAAjD,UAAA,CAAAL,kBAAA;cACAe,iBAAA,EAAAuC,MAAA,CAAAjD,UAAA,CAAAU,iBAAA;cACAC,cAAA,EAAAsC,MAAA,CAAAjD,UAAA,CAAAW,cAAA;cACA+D,QAAA;cACAC,UAAA,EAAAC,IAAA,CAAAC,SAAA;gBACAC,UAAA,EAAA7B,MAAA,CAAAjD,UAAA,CAAAL,kBAAA;gBACAgB,cAAA,EAAAsC,MAAA,CAAAjD,UAAA,CAAAW,cAAA;gBACAoE,eAAA,EAAA9B,MAAA,CAAAnB,uBAAA,CAAAkD,GAAA,WAAAC,MAAA;kBAAA;oBACA7D,EAAA,EAAA6D,MAAA,CAAA7D,EAAA;oBACAtC,IAAA,EAAAmG,MAAA,CAAAnG,IAAA;oBACAoG,IAAA,EAAAD,MAAA,CAAAC,IAAA;oBACAlB,SAAA,EAAAiB,MAAA,CAAAjB,SAAA;oBACAmB,IAAA,EAAAF,MAAA,CAAAE;kBACA;gBAAA;cACA;YACA;UACA;UACA;UAAA,KACA,IAAAlC,MAAA,CAAAjD,UAAA,CAAAO,SAAA,IAAA0C,MAAA,CAAAtB,iBAAA,IAAA4B,UAAA,CAAAE,eAAA;YACAF,UAAA,CAAAkB,oBAAA;cACA9E,kBAAA;cACAe,iBAAA,GAAAuC,MAAA,CAAAtB,iBAAA,CAAAiC,QAAA;cACAjD,cAAA;cACA+D,QAAA;cACAC,UAAA,EAAAC,IAAA,CAAAC,SAAA,CAAAtB,UAAA,CAAAE,eAAA;YACA;UACA;;UAEA;UACA2B,OAAA,CAAAC,GAAA;YACAC,GAAA,EAAArC,MAAA,CAAAjD,UAAA,CAAAO,SAAA;YACAgF,QAAA,EAAAhC,UAAA,CAAAjD,oBAAA;YACAD,QAAA,EAAAkD,UAAA,CAAAlD;UACA;UAEA3C,eAAA,CAAA6F,UAAA,EAAAZ,IAAA,WAAA6C,QAAA;YACAvC,MAAA,CAAAlD,mBAAA;YACA,IAAAyF,QAAA,CAAAC,IAAA;cACA,IAAAD,QAAA,CAAAxG,IAAA;gBACA;gBACA,IAAAgC,OAAA;gBAEA,IAAAwE,QAAA,CAAAxG,IAAA,CAAA0G,SAAA;kBACA1E,OAAA,qCAAA2E,MAAA,CAAAH,QAAA,CAAAxG,IAAA,CAAA0G,SAAA;gBACA,WAAAF,QAAA,CAAAxG,IAAA,CAAA4G,SAAA,IAAAJ,QAAA,CAAAxG,IAAA,CAAA4G,SAAA,CAAAF,SAAA;kBACA1E,OAAA,qCAAA2E,MAAA,CAAAH,QAAA,CAAAxG,IAAA,CAAA4G,SAAA,CAAAF,SAAA;gBACA;gBAEA,IAAAF,QAAA,CAAAxG,IAAA,CAAAsB,oBAAA;kBACAU,OAAA,2CAAA2E,MAAA,CAAA1C,MAAA,CAAA4C,sBAAA,CAAAL,QAAA,CAAAxG,IAAA,CAAAsB,oBAAA;gBACA;gBAEA,IAAAkF,QAAA,CAAAxG,IAAA,CAAA8G,aAAA;kBACA9E,OAAA,kCAAA2E,MAAA,CAAA1C,MAAA,CAAAtB,iBAAA,CAAAmC,UAAA;gBACA;gBAEAb,MAAA,CAAAG,QAAA,CAAA2C,OAAA,CAAA/E,OAAA;cACA;gBACAiC,MAAA,CAAAG,QAAA,CAAA2C,OAAA;cACA;cACA9C,MAAA,CAAAd,OAAA;YACA;cACAc,MAAA,CAAAG,QAAA,CAAAC,KAAA,CAAAmC,QAAA,CAAAxE,OAAA;YACA;UACA,GAAAgF,KAAA;YACA;UAAA,CACA;QACA;MACA;IACA;IAEA;IACAtD,cAAA,WAAAA,eAAA;MAAA,IAAAuD,MAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA,EAAAtH,IAAA,EAAAuH,EAAA;QAAA,OAAAJ,YAAA,GAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAT,MAAA,CAAAjE,iBAAA;cAAAyE,QAAA,CAAAE,CAAA;cAAA,MAIAV,MAAA,CAAArD,IAAA,IAAAgE,KAAA,CAAAC,OAAA,CAAAZ,MAAA,CAAArD,IAAA,CAAAkE,KAAA,KAAAb,MAAA,CAAArD,IAAA,CAAAkE,KAAA,CAAAC,QAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACA/H,aAAA;YAAA;cAAA2H,IAAA,GAAAG,QAAA,CAAAO,CAAA;cAAAP,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OAEAhI,uBAAA;YAAA;cAAA4H,IAAA,GAAAG,QAAA,CAAAO,CAAA;YAAA;cAEA,IAAAV,IAAA,IAAAA,IAAA,CAAAb,IAAA;gBACAzG,IAAA,GAAA4H,KAAA,CAAAC,OAAA,CAAAP,IAAA,CAAAtH,IAAA,IAAAsH,IAAA,CAAAtH,IAAA,GAAAsH,IAAA,CAAAtH,IAAA,IAAAsH,IAAA,CAAAtH,IAAA,CAAAiI,IAAA,GAAAX,IAAA,CAAAtH,IAAA,CAAAiI,IAAA;gBACAhB,MAAA,CAAAlE,UAAA,GAAA/C,IAAA,CAAAgG,GAAA,WAAAkC,IAAA;kBACA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAE,KAAA,KAAA5H,SAAA,SAAA0H,IAAA;kBACA;oBAAAC,KAAA,EAAAD,IAAA,CAAApI,IAAA,IAAAoI,IAAA,CAAArG,eAAA,IAAAqG,IAAA,CAAAC,KAAA;oBAAAC,KAAA,EAAAC,MAAA,CAAAH,IAAA,CAAA9F,EAAA,IAAA8F,IAAA,CAAAE,KAAA;kBAAA;gBACA;cACA;gBACAnB,MAAA,CAAAlE,UAAA;cACA;cAAA0E,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAEAf,MAAA,CAAAlE,UAAA;YAAA;cAAA0E,QAAA,CAAAE,CAAA;cAEAV,MAAA,CAAAjE,iBAAA;cAAA,OAAAyE,QAAA,CAAAa,CAAA;YAAA;cAAA,OAAAb,QAAA,CAAAc,CAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IAEA;IACAmB,oBAAA,WAAAA,qBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA9J,cAAA,CAAA6J,GAAA,CAAArG,EAAA,EAAAuB,IAAA;QACA+E,MAAA,CAAAtE,QAAA,CAAA2C,OAAA;QACA2B,MAAA,CAAAvF,OAAA;MACA;IACA;IACAwF,mBAAA,WAAAA,oBAAAF,GAAA;MAAA,IAAAG,MAAA;MACA/J,aAAA,CAAA4J,GAAA,CAAArG,EAAA,EAAAuB,IAAA;QACAiF,MAAA,CAAAxE,QAAA,CAAA2C,OAAA;QACA6B,MAAA,CAAAzF,OAAA;MACA;IACA;IACA0F,sBAAA,WAAAA,uBAAAJ,GAAA;MAAA,IAAAK,MAAA;MACAhK,gBAAA,CAAA2J,GAAA,CAAArG,EAAA,EAAAuB,IAAA;QACAmF,MAAA,CAAA1E,QAAA,CAAA2C,OAAA;QACA+B,MAAA,CAAA3F,OAAA;MACA;IACA;IACA4F,qBAAA,WAAAA,sBAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,OAAA,CAAAC,OAAA,+CAAAvC,MAAA,CAAA8B,GAAA,CAAAlI,aAAA;QACA4I,iBAAA;QACAC,gBAAA;QACAlD,IAAA;MACA,GAAAvC,IAAA;QACA5E,eAAA,CAAA0J,GAAA,CAAArG,EAAA,EAAAuB,IAAA;UACAqF,MAAA,CAAA5E,QAAA,CAAA2C,OAAA;UACAiC,MAAA,CAAA7F,OAAA;QACA;MACA,GAAA6D,KAAA;QACA;MAAA,CACA;IACA;IACAqC,oBAAA,WAAAA,qBAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,KAAAnH,SAAA;QACAC,EAAA,EAAAqG,GAAA,CAAArG,EAAA;QACAf,QAAA,EAAAoH,GAAA,CAAApH,QAAA;QAAA;QACAC,oBAAA,EAAAmH,GAAA,CAAAnH,oBAAA;MACA;MACA,KAAAY,kBAAA;MACA,KAAA2B,SAAA;QACAyF,MAAA,CAAAxF,KAAA,cAAAC,aAAA;MACA;IACA;IACAwF,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,KAAA1F,KAAA,cAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAjF,cAAA,CAAAsK,MAAA,CAAArH,SAAA,CAAAC,EAAA,EAAAoH,MAAA,CAAArH,SAAA,CAAAd,QAAA,EAAAsC,IAAA;YACA6F,MAAA,CAAAtH,kBAAA;YACAsH,MAAA,CAAApF,QAAA,CAAA2C,OAAA;YACAyC,MAAA,CAAArG,OAAA;UACA;QACA;MACA;IACA;IACAsG,cAAA,WAAAA,eAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAA5I,gBAAA,GAAA2H,GAAA;MACAzJ,gBAAA,CAAAyJ,GAAA,CAAArG,EAAA,EAAAuB,IAAA,WAAA6C,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAiD,MAAA,CAAA7I,aAAA,GAAA2F,QAAA,CAAAxG,IAAA;UACA0J,MAAA,CAAA9I,iBAAA;QACA;MACA;IACA;IACA+I,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA3K,mBAAA,GAAA0E,IAAA;QACAiG,MAAA,CAAAxF,QAAA,CAAA2C,OAAA;QACA6C,MAAA,CAAAzG,OAAA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAA0G,OAAA;MACA,KAAA3J,OAAA;MACAzB,gBAAA,MAAA2B,SAAA,EAAAuD,IAAA,WAAA6C,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAoD,OAAA,CAAA5J,SAAA,GAAAuG,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;UACA4B,OAAA,CAAA1J,KAAA,GAAAqG,QAAA,CAAAxG,IAAA,CAAA8J,UAAA;;UAEA;UACAD,OAAA,CAAAE,4BAAA;QACA;QACAF,OAAA,CAAA3J,OAAA;MACA,GAAA8G,KAAA;QACA6C,OAAA,CAAA3J,OAAA;MACA;IACA;IAEA;IACA6J,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,OAAA;MAAA,OAAA9C,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA6C,SAAA;QAAA,IAAAC,QAAA;QAAA,OAAA/C,YAAA,GAAAK,CAAA,WAAA2C,SAAA;UAAA,kBAAAA,SAAA,CAAAzC,CAAA;YAAA;cAAA,MACA,CAAAsC,OAAA,CAAA/J,SAAA,IAAA+J,OAAA,CAAA/J,SAAA,CAAAqE,MAAA;gBAAA6F,SAAA,CAAAzC,CAAA;gBAAA;cAAA;cAAA,OAAAyC,SAAA,CAAA5B,CAAA;YAAA;cAIA;cACA2B,QAAA,GAAAF,OAAA,CAAA/J,SAAA,CAAA+F,GAAA;gBAAA,IAAAoE,IAAA,GAAAlD,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAiD,SAAAzD,SAAA;kBAAA,IAAAJ,QAAA,EAAA8D,eAAA,EAAAC,GAAA;kBAAA,OAAApD,YAAA,GAAAK,CAAA,WAAAgD,SAAA;oBAAA,kBAAAA,SAAA,CAAA9C,CAAA;sBAAA;wBAAA8C,SAAA,CAAA7C,CAAA;wBAAA6C,SAAA,CAAA9C,CAAA;wBAAA,OAEAnI,2BAAA,CAAAqH,SAAA,CAAAxE,EAAA;sBAAA;wBAAAoE,QAAA,GAAAgE,SAAA,CAAAxC,CAAA;wBACA,IAAAxB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAxG,IAAA;0BACA;0BACAsK,eAAA;0BACA,IAAA1C,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAxG,IAAA;4BACA;4BACAsK,eAAA,GAAA9D,QAAA,CAAAxG,IAAA;0BACA,WAAAwG,QAAA,CAAAxG,IAAA,CAAAiI,IAAA,IAAAL,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;4BACA;4BACAqC,eAAA,GAAA9D,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;0BACA;0BACA;0BACA+B,OAAA,CAAAS,IAAA,CAAA7D,SAAA,qBAAA0D,eAAA;wBACA;wBAAAE,SAAA,CAAA9C,CAAA;wBAAA;sBAAA;wBAAA8C,SAAA,CAAA7C,CAAA;wBAAA4C,GAAA,GAAAC,SAAA,CAAAxC,CAAA;wBAEA5B,OAAA,CAAAsE,IAAA,4BAAA/D,MAAA,CAAAC,SAAA,CAAAxE,EAAA,8DAAAmI,GAAA;sBAAA;wBAAA,OAAAC,SAAA,CAAAjC,CAAA;oBAAA;kBAAA,GAAA8B,QAAA;gBAAA,CAEA;gBAAA,iBAAAM,EAAA;kBAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAAV,SAAA,CAAAzC,CAAA;cAAA,OAEAnE,OAAA,CAAAC,GAAA,CAAA0G,QAAA;YAAA;cAAA,OAAAC,SAAA,CAAA5B,CAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IACA;IACAa,WAAA,WAAAA,YAAAC,YAAA;MACA,KAAAA,YAAA;MACA;QACA,IAAAC,KAAA,GAAApF,IAAA,CAAAqF,KAAA,CAAAF,YAAA;QACA,IAAAnD,KAAA,CAAAC,OAAA,CAAAmD,KAAA,KAAAA,KAAA,CAAA1G,MAAA;UACA;UACA,OAAA0G,KAAA,CAAAhF,GAAA,WAAA2B,CAAA;YAAA,UAAAhB,MAAA,CAAAgB,CAAA,CAAAuD,QAAA,cAAAvE,MAAA,CAAAgB,CAAA,CAAAwD,QAAA;UAAA,GAAAC,IAAA;QACA;QACA;MACA,SAAAC,CAAA;QACA,OAAAN,YAAA;MACA;IACA;IACA;IACAO,UAAA,WAAAA,WAAAC,QAAA;MACA,KAAAA,QAAA;MACA,WAAAC,IAAA,CAAAD,QAAA,EAAAE,cAAA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,SAAA,CAAAC,SAAA;QACAD,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAJ,IAAA,EAAAhI,IAAA;UACAiI,OAAA,CAAAxH,QAAA,CAAA2C,OAAA;QACA;MACA;QACA;QACA,IAAAiF,QAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,QAAA,CAAA5D,KAAA,GAAAuD,IAAA;QACAM,QAAA,CAAAE,IAAA,CAAAC,WAAA,CAAAJ,QAAA;QACAA,QAAA,CAAAK,MAAA;QACAJ,QAAA,CAAAK,WAAA;QACAL,QAAA,CAAAE,IAAA,CAAAI,WAAA,CAAAP,QAAA;QACA,KAAA5H,QAAA,CAAA2C,OAAA;MACA;IACA;IAEA;IACAyF,iBAAA,WAAAA,kBAAA/D,GAAA;MAAA,IAAAgE,OAAA;MAAA,OAAAvF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAsF,SAAA;QAAA,IAAAlG,QAAA,EAAAmG,cAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,GAAA;QAAA,OAAA3F,YAAA,GAAAK,CAAA,WAAAuF,SAAA;UAAA,kBAAAA,SAAA,CAAArF,CAAA;YAAA;cAAAqF,SAAA,CAAApF,CAAA;cAAAoF,SAAA,CAAArF,CAAA;cAAA,OAGAlI,qBAAA,CAAAiJ,GAAA,CAAArG,EAAA;YAAA;cAAAoE,QAAA,GAAAuG,SAAA,CAAA/E,CAAA;cACA,IAAAxB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAxG,IAAA;gBAAA2M,cAAA,GACAnG,QAAA,CAAAxG,IAAA,EAAA4M,UAAA,GAAAD,cAAA,CAAAC,UAAA,EAAAC,UAAA,GAAAF,cAAA,CAAAE,UAAA;gBAEA,IAAAD,UAAA,IAAAA,UAAA;kBACA;kBACAH,OAAA,CAAAO,MAAA,IAAArG,MAAA,CACAkG,UAAA,+IACA,UACA;oBACA1D,iBAAA;oBACAjD,IAAA;kBACA,CACA;gBACA;kBACA;kBACAuG,OAAA,CAAAO,MAAA,CACA,iDACA,SACA;oBACA7D,iBAAA;oBACAjD,IAAA;kBACA,CACA;gBACA;cACA;gBACAuG,OAAA,CAAArI,QAAA,CAAAC,KAAA,kBAAAmC,QAAA,CAAAxE,OAAA;cACA;cAAA+K,SAAA,CAAArF,CAAA;cAAA;YAAA;cAAAqF,SAAA,CAAApF,CAAA;cAAAmF,GAAA,GAAAC,SAAA,CAAA/E,CAAA;cAEA5B,OAAA,CAAA/B,KAAA,gBAAAyI,GAAA;cACAL,OAAA,CAAArI,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAA0I,SAAA,CAAAxE,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA;IAEA;IAEA;IACAO,iBAAA,WAAAA,kBAAAxE,GAAA;MAAA,IAAAyE,OAAA;MACA,KAAAC,OAAA;QACAhE,iBAAA;QACAC,gBAAA;QACAgE,cAAA,WAAAA,eAAAhF,KAAA;UACA,IAAAjC,IAAA,GAAAkH,QAAA,CAAAjF,KAAA;UACA,KAAAjC,IAAA,IAAAA,IAAA,QAAAA,IAAA;YACA;UACA;UACA;QACA;MACA,GAAAxC,IAAA,WAAA2J,KAAA;QAAA,IAAAlF,KAAA,GAAAkF,KAAA,CAAAlF,KAAA;QACA,IAAAmF,WAAA;UACAC,WAAA,EAAA/E,GAAA,CAAArG,EAAA;UACAhB,WAAA,EAAAiM,QAAA,CAAAjF,KAAA;QACA;QAEA8E,OAAA,CAAA7N,oBAAA,CAAAkO,WAAA,EAAA5J,IAAA,WAAA6C,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAxG,IAAA,IAAAwG,QAAA,CAAAxG,IAAA,CAAA0G,SAAA;YACAwG,OAAA,CAAA9I,QAAA,CAAA2C,OAAA,sEAAAJ,MAAA,CAAAH,QAAA,CAAAxG,IAAA,CAAA0G,SAAA;YACAwG,OAAA,CAAA/J,OAAA;UACA;YACA+J,OAAA,CAAA9I,QAAA,CAAAC,KAAA,CAAAmC,QAAA,CAAAxE,OAAA;UACA;QACA,GAAAgF,KAAA;UACA;QAAA,CACA;MACA,GAAAA,KAAA;QACA;MAAA,CACA;IACA;IAIA;IACA5H,oBAAA,WAAAA,qBAAAmO,WAAA;MAAA,OAAArG,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAqG,SAAA;QAAA,OAAAtG,YAAA,GAAAK,CAAA,WAAAkG,SAAA;UAAA,kBAAAA,SAAA,CAAAhG,CAAA;YAAA;cAAA,OAAAgG,SAAA,CAAAnF,CAAA,IACAnJ,qBAAA,CAAAmO,WAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEA;IACApO,oBAAA,WAAAA,qBAAAkO,WAAA;MAAA,OAAArG,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAuG,SAAA;QAAA,OAAAxG,YAAA,GAAAK,CAAA,WAAAoG,SAAA;UAAA,kBAAAA,SAAA,CAAAlG,CAAA;YAAA;cAAA,OAAAkG,SAAA,CAAArF,CAAA,IACAlJ,qBAAA,CAAAkO,WAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IACA;IAEA;IACAlK,aAAA,WAAAA,cAAA;MAAA,IAAAoK,OAAA;MAAA,OAAA3G,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA0G,SAAA;QAAA,IAAAtH,QAAA,EAAAuH,GAAA;QAAA,OAAA5G,YAAA,GAAAK,CAAA,WAAAwG,SAAA;UAAA,kBAAAA,SAAA,CAAAtG,CAAA;YAAA;cACAmG,OAAA,CAAAtL,gBAAA;cAAAyL,SAAA,CAAArG,CAAA;cAAAqG,SAAA,CAAAtG,CAAA;cAAA,OAEAjI,YAAA;YAAA;cAAA+G,QAAA,GAAAwH,SAAA,CAAAhG,CAAA;cACA,IAAAxB,QAAA,CAAAC,IAAA;gBACA;gBACA,IAAAD,QAAA,CAAAxG,IAAA,IAAAwG,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;kBACA;kBACA4F,OAAA,CAAAvL,SAAA,GAAAkE,QAAA,CAAAxG,IAAA,CAAAiI,IAAA,CAAAjC,GAAA,WAAAiI,KAAA;oBACA,OAAAzJ,aAAA,CAAAA,aAAA,KACAyJ,KAAA;sBACA;sBACAC,MAAA,SAAAD,KAAA,CAAAC,MAAA,gBAAAb,QAAA,CAAAY,KAAA,CAAAC,MAAA,IAAAD,KAAA,CAAAC;oBAAA;kBAEA;gBACA,WAAAtG,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAxG,IAAA;kBACA;kBACA6N,OAAA,CAAAvL,SAAA,GAAAkE,QAAA,CAAAxG,IAAA,CAAAgG,GAAA,WAAAiI,KAAA;oBACA,OAAAzJ,aAAA,CAAAA,aAAA,KACAyJ,KAAA;sBACAC,MAAA,SAAAD,KAAA,CAAAC,MAAA,gBAAAb,QAAA,CAAAY,KAAA,CAAAC,MAAA,IAAAD,KAAA,CAAAC;oBAAA;kBAEA;gBACA;kBACAL,OAAA,CAAAvL,SAAA;gBACA;gBAEA,IAAAuL,OAAA,CAAAvL,SAAA,CAAAgC,MAAA;kBACAuJ,OAAA,CAAAzJ,QAAA,CAAA+J,IAAA;gBACA;cACA;gBACAN,OAAA,CAAAzJ,QAAA,CAAAC,KAAA,gBAAAmC,QAAA,CAAAxE,OAAA;gBACA6L,OAAA,CAAAvL,SAAA;cACA;cAAA0L,SAAA,CAAAtG,CAAA;cAAA;YAAA;cAAAsG,SAAA,CAAArG,CAAA;cAAAoG,GAAA,GAAAC,SAAA,CAAAhG,CAAA;cAEA5B,OAAA,CAAA/B,KAAA,cAAA0J,GAAA;cACAF,OAAA,CAAAzJ,QAAA,CAAAC,KAAA;cACAwJ,OAAA,CAAAvL,SAAA;YAAA;cAAA0L,SAAA,CAAArG,CAAA;cAEAkG,OAAA,CAAAtL,gBAAA;cAAA,OAAAyL,SAAA,CAAA1F,CAAA;YAAA;cAAA,OAAA0F,SAAA,CAAAzF,CAAA;UAAA;QAAA,GAAAuF,QAAA;MAAA;IAEA;IAEA;IACAM,iBAAA,WAAAA,kBAAAnN,OAAA;MACA,IAAAA,OAAA;QACA,KAAAuB,aAAA,QAAAF,SAAA,CAAA+L,IAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAAlM,EAAA,KAAAnB,OAAA;QAAA;QACA,SAAAuB,aAAA;UACA;UACA,KAAAxB,UAAA,CAAAE,SAAA,QAAAsB,aAAA,CAAA1C,IAAA;UACA,KAAAkB,UAAA,CAAAG,QAAA,QAAAqB,aAAA,CAAA+L,GAAA;;UAEA;UACA,UAAAvN,UAAA,CAAAT,aAAA;YACA,KAAAiO,oBAAA,MAAAhM,aAAA,CAAA1C,IAAA,OAAA0C,aAAA,CAAA+L,GAAA;UACA;;UAEA;UACA,KAAAE,kBAAA,MAAAjM,aAAA,CAAA1C,IAAA;QACA;MACA;QACA,KAAA0C,aAAA;QACA,KAAAxB,UAAA,CAAAE,SAAA;QACA,KAAAF,UAAA,CAAAG,QAAA;MACA;IACA;IAEA;IACAqN,oBAAA,WAAAA,qBAAAtN,SAAA,EAAAC,QAAA;MACA;MACA,IAAAuN,QAAA,GAAAxN,SAAA,CAAAyN,OAAA,uBAAAC,WAAA;;MAEA;MACA,IAAAzN,QAAA,IAAAA,QAAA;QACAuN,QAAA,UAAAvN,QAAA,CAAAwN,OAAA;MACA;;MAEA;MACA,IAAAE,SAAA,GAAArD,IAAA,CAAAsD,GAAA,GAAAC,QAAA,GAAAC,KAAA;MACA,KAAAhO,UAAA,CAAAT,aAAA,MAAAoG,MAAA,CAAA+H,QAAA,OAAA/H,MAAA,CAAAkI,SAAA;IACA;IAEA;IACAJ,kBAAA,WAAAA,mBAAAvN,SAAA;MACA,IAAA+N,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA,SAAAC,EAAA,MAAAC,eAAA,GAAAC,MAAA,CAAAC,OAAA,CAAAJ,OAAA,GAAAC,EAAA,GAAAC,eAAA,CAAA7K,MAAA,EAAA4K,EAAA;QAAA,IAAAI,kBAAA,GAAAC,cAAA,CAAAJ,eAAA,CAAAD,EAAA;UAAAM,GAAA,GAAAF,kBAAA;UAAAnJ,IAAA,GAAAmJ,kBAAA;QACA,IAAApO,SAAA,CAAA0N,WAAA,GAAA7G,QAAA,CAAAyH,GAAA;UACA,KAAAxO,UAAA,CAAAI,WAAA,GAAA+E,IAAA;UACA;QACA;MACA;IACA;IAEA;IACAsJ,eAAA,WAAAA,gBAAAvB,MAAA;MACA,KAAAA,MAAA,IAAAA,MAAA;;MAEA;MACA,IAAAwB,IAAA,UAAAxB,MAAA,gBAAAb,QAAA,CAAAa,MAAA,IAAAA,MAAA;MACA,IAAAyB,KAAA,CAAAD,IAAA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAA/I,MAAA,CAAA+I,IAAA;MACA;;MAEA;MACA,IAAAE,MAAA,GAAAF,IAAA;MACA,UAAA/I,MAAA,CAAAiJ,MAAA,CAAAC,OAAA;IACA;IAEA;IACAhJ,sBAAA,WAAAA,uBAAAN,QAAA;MACA,IAAAuJ,WAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAvJ,QAAA,KAAAA,QAAA;IACA;IAEA;IACAwJ,kCAAA,WAAAA,mCAAA1O,QAAA;MAAA,OAAA6F,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA4I,SAAA;QAAA,IAAAxJ,QAAA,EAAAyJ,GAAA;QAAA,OAAA9I,YAAA,GAAAK,CAAA,WAAA0I,SAAA;UAAA,kBAAAA,SAAA,CAAAxI,CAAA;YAAA;cAAAwI,SAAA,CAAAvI,CAAA;cAAAuI,SAAA,CAAAxI,CAAA;cAAA,OAEA/I,sBAAA,CAAA0C,QAAA;YAAA;cAAAmF,QAAA,GAAA0J,SAAA,CAAAlI,CAAA;cAAA,MACAxB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAxG,IAAA;gBAAAkQ,SAAA,CAAAxI,CAAA;gBAAA;cAAA;cAAA,OAAAwI,SAAA,CAAA3H,CAAA,IACA/B,QAAA,CAAAxG,IAAA,CAAAmQ,mBAAA;YAAA;cAAAD,SAAA,CAAAxI,CAAA;cAAA;YAAA;cAAAwI,SAAA,CAAAvI,CAAA;cAAAsI,GAAA,GAAAC,SAAA,CAAAlI,CAAA;cAGA5B,OAAA,CAAAsE,IAAA,gBAAAuF,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA3H,CAAA,IAEA;UAAA;QAAA,GAAAyH,QAAA;MAAA;IACA;IAEA;IAEA;IACAI,0BAAA,WAAAA,2BAAAC,OAAA;MACA,IAAAA,OAAA;QACA;QACA,KAAArP,UAAA,CAAAL,kBAAA;QACA,KAAAK,UAAA,CAAAU,iBAAA;QACA,KAAAV,UAAA,CAAAW,cAAA;QACA,KAAAmB,uBAAA;QACA,KAAAF,wBAAA;QACA;QACA,KAAA0N,sBAAA;MACA;QACA;QACA,KAAAtP,UAAA,CAAAL,kBAAA;QACA,KAAAK,UAAA,CAAAU,iBAAA;QACA,KAAAoB,uBAAA;QACA,KAAAF,wBAAA;MACA;IACA;IAEA;IACA2N,sBAAA,WAAAA,uBAAAzK,UAAA;MACA,IAAAA,UAAA;QACA;QACA,KAAA9E,UAAA,CAAAU,iBAAA;QACA,KAAAoB,uBAAA;QACA;QACA,KAAAwN,sBAAA;MACA;QACA,KAAA1N,wBAAA;QACA,KAAAE,uBAAA;MACA;IACA;IAEA;IACAwN,sBAAA,WAAAA,uBAAA;MAAA,IAAAE,OAAA;MAAA,OAAAtJ,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAqJ,SAAA;QAAA,IAAAjK,QAAA,EAAAkK,UAAA,EAAAC,GAAA;QAAA,OAAAxJ,YAAA,GAAAK,CAAA,WAAAoJ,SAAA;UAAA,kBAAAA,SAAA,CAAAlJ,CAAA;YAAA;cAAA,IACA8I,OAAA,CAAAxP,UAAA,CAAAL,kBAAA;gBAAAiQ,SAAA,CAAAlJ,CAAA;gBAAA;cAAA;cAAA,OAAAkJ,SAAA,CAAArI,CAAA;YAAA;cAIAiI,OAAA,CAAA3N,yBAAA;cAAA+N,SAAA,CAAAjJ,CAAA;cAAAiJ,SAAA,CAAAlJ,CAAA;cAAA,OAEApI,2BAAA;gBACAqB,kBAAA,EAAA6P,OAAA,CAAAxP,UAAA,CAAAL,kBAAA;gBACAkQ,kBAAA;gBAAA;gBACAlP,cAAA,EAAA6O,OAAA,CAAAxP,UAAA,CAAAW,cAAA;cACA;YAAA;cAJA6E,QAAA,GAAAoK,SAAA,CAAA5I,CAAA;cAMA5B,OAAA,CAAAC,GAAA,eAAAG,QAAA;;cAEA,IAAAA,QAAA,CAAAC,IAAA;gBACA;gBACAiK,UAAA;gBACA,IAAAlK,QAAA,CAAAxG,IAAA,IAAA4H,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;kBACA;kBACAyI,UAAA,GAAAlK,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;gBACA,WAAAL,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAxG,IAAA;kBACA;kBACA0Q,UAAA,GAAAlK,QAAA,CAAAxG,IAAA;gBACA;kBACA0Q,UAAA;gBACA;gBAEAF,OAAA,CAAA5N,wBAAA,GAAA8N,UAAA;gBAEA,IAAAF,OAAA,CAAA5N,wBAAA,CAAA0B,MAAA;kBACAkM,OAAA,CAAApM,QAAA,CAAA+J,IAAA,8CAAAxH,MAAA,CAAA6J,OAAA,CAAAxP,UAAA,CAAAL,kBAAA;gBACA;kBACAyF,OAAA,CAAAC,GAAA,aAAAmK,OAAA,CAAA5N,wBAAA,CAAA0B,MAAA;gBACA;cACA;gBACAkM,OAAA,CAAApM,QAAA,CAAAC,KAAA,kBAAAmC,QAAA,CAAAxE,OAAA;gBACAwO,OAAA,CAAA5N,wBAAA;cACA;cAAAgO,SAAA,CAAAlJ,CAAA;cAAA;YAAA;cAAAkJ,SAAA,CAAAjJ,CAAA;cAAAgJ,GAAA,GAAAC,SAAA,CAAA5I,CAAA;cAEA5B,OAAA,CAAA/B,KAAA,gBAAAsM,GAAA;cACAH,OAAA,CAAApM,QAAA,CAAAC,KAAA;cACAmM,OAAA,CAAA5N,wBAAA;YAAA;cAAAgO,SAAA,CAAAjJ,CAAA;cAEA6I,OAAA,CAAA3N,yBAAA;cAAA,OAAA+N,SAAA,CAAAtI,CAAA;YAAA;cAAA,OAAAsI,SAAA,CAAArI,CAAA;UAAA;QAAA,GAAAkI,QAAA;MAAA;IAEA;IAEA;IACAK,0BAAA,WAAAA,2BAAAC,SAAA;MAAA,IAAAC,OAAA;MACA,KAAAlO,uBAAA;MACA,IAAAiO,SAAA,IAAAA,SAAA,CAAAzM,MAAA;QACAyM,SAAA,CAAAE,OAAA,WAAArM,QAAA;UACA,IAAAqB,MAAA,GAAA+K,OAAA,CAAApO,wBAAA,CAAAyL,IAAA,WAAA6C,CAAA;YAAA,OAAAA,CAAA,CAAA9O,EAAA,KAAAwC,QAAA;UAAA;UACA,IAAAqB,MAAA;YACA+K,OAAA,CAAAlO,uBAAA,CAAAqO,IAAA,CAAAlL,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAmL,kBAAA,WAAAA,mBAAAtL,UAAA;MACA,IAAAuL,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAvL,UAAA;IACA;IAEA;IAEA;IACAwL,eAAA,WAAAA,gBAAAjB,OAAA;MACA,IAAAA,OAAA;QACA;QACA,KAAAkB,iBAAA;MACA;QACA;QACA,KAAAvQ,UAAA,CAAAQ,WAAA;QACA,KAAAmB,iBAAA;MACA;IACA;IAEA;IACA4O,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,OAAAtK,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAqK,SAAA;QAAA,IAAAjL,QAAA,EAAAkL,GAAA;QAAA,OAAAvK,YAAA,GAAAK,CAAA,WAAAmK,SAAA;UAAA,kBAAAA,SAAA,CAAAjK,CAAA;YAAA;cACA8J,OAAA,CAAA9O,oBAAA;cAAAiP,SAAA,CAAAhK,CAAA;cAAAgK,SAAA,CAAAjK,CAAA;cAAA,OAEA9H,sBAAA;gBACAa,MAAA;cACA;YAAA;cAFA+F,QAAA,GAAAmL,SAAA,CAAA3J,CAAA;cAIA,IAAAxB,QAAA,CAAAC,IAAA;gBACA,IAAAD,QAAA,CAAAxG,IAAA,IAAAwG,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;kBACAuJ,OAAA,CAAA/O,aAAA,GAAA+D,QAAA,CAAAxG,IAAA,CAAAiI,IAAA;gBACA,WAAAL,KAAA,CAAAC,OAAA,CAAArB,QAAA,CAAAxG,IAAA;kBACAwR,OAAA,CAAA/O,aAAA,GAAA+D,QAAA,CAAAxG,IAAA;gBACA;kBACAwR,OAAA,CAAA/O,aAAA;gBACA;gBAEA,IAAA+O,OAAA,CAAA/O,aAAA,CAAA6B,MAAA;kBACAkN,OAAA,CAAApN,QAAA,CAAA+J,IAAA;gBACA;cACA;gBACAqD,OAAA,CAAApN,QAAA,CAAAC,KAAA,mBAAAmC,QAAA,CAAAxE,OAAA;gBACAwP,OAAA,CAAA/O,aAAA;cACA;cAAAkP,SAAA,CAAAjK,CAAA;cAAA;YAAA;cAAAiK,SAAA,CAAAhK,CAAA;cAAA+J,GAAA,GAAAC,SAAA,CAAA3J,CAAA;cAEA5B,OAAA,CAAA/B,KAAA,iBAAAqN,GAAA;cACAF,OAAA,CAAApN,QAAA,CAAAC,KAAA;cACAmN,OAAA,CAAA/O,aAAA;YAAA;cAAAkP,SAAA,CAAAhK,CAAA;cAEA6J,OAAA,CAAA9O,oBAAA;cAAA,OAAAiP,SAAA,CAAArJ,CAAA;YAAA;cAAA,OAAAqJ,SAAA,CAAApJ,CAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA;IAEA;IAEA;IACAG,qBAAA,WAAAA,sBAAAhN,QAAA;MACA,IAAAA,QAAA;QACA,KAAAjC,iBAAA,QAAAF,aAAA,CAAA4L,IAAA,WAAApI,MAAA;UAAA,OAAAA,MAAA,CAAArB,QAAA,KAAAA,QAAA;QAAA;QACA,SAAAjC,iBAAA;UACA;UACA,KAAAkP,mBAAA,CAAAjN,QAAA;QACA;MACA;QACA,KAAAjC,iBAAA;MACA;IACA;IAEA;IACAkP,mBAAA,WAAAA,oBAAAjN,QAAA;MAAA,IAAAkN,OAAA;MAAA,OAAA5K,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA2K,SAAA;QAAA,IAAAvL,QAAA,EAAAwL,GAAA;QAAA,OAAA7K,YAAA,GAAAK,CAAA,WAAAyK,SAAA;UAAA,kBAAAA,SAAA,CAAAvK,CAAA;YAAA;cAAAuK,SAAA,CAAAtK,CAAA;cAAAsK,SAAA,CAAAvK,CAAA;cAAA,OAEA7H,kBAAA,CAAA+E,QAAA;YAAA;cAAA4B,QAAA,GAAAyL,SAAA,CAAAjK,CAAA;cACA,IAAAxB,QAAA,CAAAC,IAAA,cAAAD,QAAA,CAAAxG,IAAA;gBACA8R,OAAA,CAAAnP,iBAAA,GAAA6D,QAAA,CAAAxG,IAAA;cACA;cAAAiS,SAAA,CAAAvK,CAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAtK,CAAA;cAAAqK,GAAA,GAAAC,SAAA,CAAAjK,CAAA;cAEA5B,OAAA,CAAAsE,IAAA,mBAAAsH,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA1J,CAAA;UAAA;QAAA,GAAAwJ,QAAA;MAAA;IAEA;EAGA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAA/O,OAAA;IACA;IACA,KAAAM,aAAA;IACA;IACA,KAAA8N,iBAAA;IACA;IACA,KAAA7N,cAAA;EACA;EACAyO,QAAA,EAAA3N,aAAA,KACAjG,UAAA,EACA,OACA,EACA;EACA6T,KAAA;IACA;IACA,gCAAAC,mBAAAC,WAAA,EAAAC,WAAA;MAAA,IAAAC,OAAA;MACA,IAAAF,WAAA,QAAAA,WAAA,KAAAC,WAAA;QACA,KAAAxC,kCAAA,CAAAuC,WAAA,EAAA3O,IAAA,WAAA4C,QAAA;UACAiM,OAAA,CAAAxR,UAAA,CAAAM,oBAAA,GAAAiF,QAAA;QACA;MACA,WAAA+L,WAAA;QACA;QACA,KAAAtR,UAAA,CAAAM,oBAAA;MACA;IACA;IACA;IACA,+BAAAmR,kBAAAH,WAAA,EAAAC,WAAA;MAAA,IAAAG,OAAA;MACA,IAAAJ,WAAA,QAAAA,WAAA,KAAAC,WAAA;QACA,KAAAxC,kCAAA,CAAAuC,WAAA,EAAA3O,IAAA,WAAA4C,QAAA;UACAmM,OAAA,CAAAvQ,SAAA,CAAAb,oBAAA,GAAAiF,QAAA;QACA;MACA,WAAA+L,WAAA;QACA,KAAAnQ,SAAA,CAAAb,oBAAA;MACA;IACA;IACA;IACA,sCAAAqR,yBAAAC,iBAAA,EAAAC,iBAAA;MACA,IAAAD,iBAAA,KAAAC,iBAAA,SAAA7R,UAAA,CAAAL,kBAAA;QACA;QACA,KAAAK,UAAA,CAAAU,iBAAA;QACA,KAAAoB,uBAAA;QACA;QACA,KAAAwN,sBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}