package tech.tongdao.ccsp.management.modules.swarm.converter;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tech.tongdao.ccsp.management.modules.swarm.entity.ApplicationContainerConfigEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.BusinessContainerDeploymentEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.BusinessContainerRelEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.ContainerInstanceEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.DockerImageEntity;
import tech.tongdao.ccsp.management.modules.swarm.entity.SwarmNodeEntity;
import tech.tongdao.ccsp.management.modules.swarm.form.ApplicationContainerConfigForm;
import tech.tongdao.ccsp.management.modules.swarm.form.BusinessContainerDeploymentForm;
import tech.tongdao.ccsp.management.modules.swarm.form.BusinessContainerRelForm;
import tech.tongdao.ccsp.management.modules.swarm.form.ContainerInstanceForm;
import tech.tongdao.ccsp.management.modules.swarm.form.DockerImageForm;
import tech.tongdao.ccsp.management.modules.swarm.form.SwarmNodeForm;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-05T15:25:39+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class SwarmEntityMapperImpl implements SwarmEntityMapper {

    @Override
    public SwarmNodeForm toSwarmNodeForm(SwarmNodeEntity entity) {
        if ( entity == null ) {
            return null;
        }

        SwarmNodeForm swarmNodeForm = new SwarmNodeForm();

        swarmNodeForm.setArchitecture( entity.getArchitecture() );
        swarmNodeForm.setAvailability( entity.getAvailability() );
        swarmNodeForm.setCpuCores( entity.getCpuCores() );
        swarmNodeForm.setCreateTime( entity.getCreateTime() );
        swarmNodeForm.setDiskGb( entity.getDiskGb() );
        swarmNodeForm.setDockerVersion( entity.getDockerVersion() );
        swarmNodeForm.setHostname( entity.getHostname() );
        swarmNodeForm.setId( entity.getId() );
        swarmNodeForm.setIpAddress( entity.getIpAddress() );
        swarmNodeForm.setLabels( entity.getLabels() );
        swarmNodeForm.setLastHeartbeat( entity.getLastHeartbeat() );
        swarmNodeForm.setMemoryMb( entity.getMemoryMb() );
        swarmNodeForm.setNodeId( entity.getNodeId() );
        swarmNodeForm.setNodeName( entity.getNodeName() );
        swarmNodeForm.setOsType( entity.getOsType() );
        swarmNodeForm.setRole( entity.getRole() );
        swarmNodeForm.setStatus( entity.getStatus() );
        swarmNodeForm.setUpdateTime( entity.getUpdateTime() );

        return swarmNodeForm;
    }

    @Override
    public List<SwarmNodeForm> toSwarmNodeFormList(List<SwarmNodeEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SwarmNodeForm> list = new ArrayList<SwarmNodeForm>( entities.size() );
        for ( SwarmNodeEntity swarmNodeEntity : entities ) {
            list.add( toSwarmNodeForm( swarmNodeEntity ) );
        }

        return list;
    }

    @Override
    public ContainerInstanceForm toContainerInstanceForm(ContainerInstanceEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ContainerInstanceForm containerInstanceForm = new ContainerInstanceForm();

        containerInstanceForm.setApplicationId( entity.getApplicationId() );
        containerInstanceForm.setContainerId( entity.getContainerId() );
        containerInstanceForm.setContainerName( entity.getContainerName() );
        containerInstanceForm.setCreateTime( entity.getCreateTime() );
        containerInstanceForm.setDeployConfig( entity.getDeployConfig() );
        containerInstanceForm.setDeployTime( entity.getDeployTime() );
        containerInstanceForm.setDeployedBy( entity.getDeployedBy() );
        containerInstanceForm.setEnvironmentVars( entity.getEnvironmentVars() );
        containerInstanceForm.setId( entity.getId() );
        containerInstanceForm.setImageId( entity.getImageId() );
        containerInstanceForm.setImageName( entity.getImageName() );
        containerInstanceForm.setImageTag( entity.getImageTag() );
        containerInstanceForm.setNodeId( entity.getNodeId() );
        containerInstanceForm.setNodeName( entity.getNodeName() );
        containerInstanceForm.setPortMappings( entity.getPortMappings() );
        containerInstanceForm.setResourceLimits( entity.getResourceLimits() );
        containerInstanceForm.setServiceId( entity.getServiceId() );
        containerInstanceForm.setStartTime( entity.getStartTime() );
        containerInstanceForm.setStatus( entity.getStatus() );
        containerInstanceForm.setStopTime( entity.getStopTime() );
        containerInstanceForm.setUpdateTime( entity.getUpdateTime() );
        containerInstanceForm.setVolumeMounts( entity.getVolumeMounts() );

        return containerInstanceForm;
    }

    @Override
    public List<ContainerInstanceForm> toContainerInstanceFormList(List<ContainerInstanceEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ContainerInstanceForm> list = new ArrayList<ContainerInstanceForm>( entities.size() );
        for ( ContainerInstanceEntity containerInstanceEntity : entities ) {
            list.add( toContainerInstanceForm( containerInstanceEntity ) );
        }

        return list;
    }

    @Override
    public DockerImageForm toDockerImageForm(DockerImageEntity entity) {
        if ( entity == null ) {
            return null;
        }

        DockerImageForm dockerImageForm = new DockerImageForm();

        dockerImageForm.setArchitecture( entity.getArchitecture() );
        dockerImageForm.setCreateTime( entity.getCreateTime() );
        dockerImageForm.setCreatedBy( entity.getCreatedBy() );
        dockerImageForm.setDescription( entity.getDescription() );
        dockerImageForm.setId( entity.getId() );
        dockerImageForm.setName( entity.getName() );
        dockerImageForm.setOs( entity.getOs() );
        dockerImageForm.setRepository( entity.getRepository() );
        dockerImageForm.setSizeMb( entity.getSizeMb() );
        dockerImageForm.setStatus( entity.getStatus() );
        dockerImageForm.setTag( entity.getTag() );
        dockerImageForm.setUpdateTime( entity.getUpdateTime() );

        return dockerImageForm;
    }

    @Override
    public List<DockerImageForm> toDockerImageFormList(List<DockerImageEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<DockerImageForm> list = new ArrayList<DockerImageForm>( entities.size() );
        for ( DockerImageEntity dockerImageEntity : entities ) {
            list.add( toDockerImageForm( dockerImageEntity ) );
        }

        return list;
    }

    @Override
    public ContainerInstanceEntity toContainerInstanceEntity(ContainerInstanceForm form) {
        if ( form == null ) {
            return null;
        }

        ContainerInstanceEntity containerInstanceEntity = new ContainerInstanceEntity();

        containerInstanceEntity.setApplicationId( form.getApplicationId() );
        containerInstanceEntity.setContainerId( form.getContainerId() );
        containerInstanceEntity.setContainerName( form.getContainerName() );
        containerInstanceEntity.setCreateTime( form.getCreateTime() );
        containerInstanceEntity.setDeployConfig( form.getDeployConfig() );
        containerInstanceEntity.setDeployTime( form.getDeployTime() );
        containerInstanceEntity.setDeployedBy( form.getDeployedBy() );
        containerInstanceEntity.setEnvironmentVars( form.getEnvironmentVars() );
        containerInstanceEntity.setId( form.getId() );
        containerInstanceEntity.setImageId( form.getImageId() );
        containerInstanceEntity.setImageName( form.getImageName() );
        containerInstanceEntity.setImageTag( form.getImageTag() );
        containerInstanceEntity.setNodeId( form.getNodeId() );
        containerInstanceEntity.setNodeName( form.getNodeName() );
        containerInstanceEntity.setPortMappings( form.getPortMappings() );
        containerInstanceEntity.setResourceLimits( form.getResourceLimits() );
        containerInstanceEntity.setServiceId( form.getServiceId() );
        containerInstanceEntity.setStartTime( form.getStartTime() );
        containerInstanceEntity.setStatus( form.getStatus() );
        containerInstanceEntity.setStopTime( form.getStopTime() );
        containerInstanceEntity.setUpdateTime( form.getUpdateTime() );
        containerInstanceEntity.setVolumeMounts( form.getVolumeMounts() );

        return containerInstanceEntity;
    }

    @Override
    public DockerImageEntity toDockerImageEntity(DockerImageForm form) {
        if ( form == null ) {
            return null;
        }

        DockerImageEntity dockerImageEntity = new DockerImageEntity();

        dockerImageEntity.setArchitecture( form.getArchitecture() );
        dockerImageEntity.setCreateTime( form.getCreateTime() );
        dockerImageEntity.setCreatedBy( form.getCreatedBy() );
        dockerImageEntity.setDescription( form.getDescription() );
        dockerImageEntity.setId( form.getId() );
        dockerImageEntity.setName( form.getName() );
        dockerImageEntity.setOs( form.getOs() );
        dockerImageEntity.setRepository( form.getRepository() );
        dockerImageEntity.setSizeMb( form.getSizeMb() );
        dockerImageEntity.setStatus( form.getStatus() );
        dockerImageEntity.setTag( form.getTag() );
        dockerImageEntity.setUpdateTime( form.getUpdateTime() );

        return dockerImageEntity;
    }

    @Override
    public SwarmNodeEntity toSwarmNodeEntity(SwarmNodeForm form) {
        if ( form == null ) {
            return null;
        }

        SwarmNodeEntity swarmNodeEntity = new SwarmNodeEntity();

        swarmNodeEntity.setArchitecture( form.getArchitecture() );
        swarmNodeEntity.setAvailability( form.getAvailability() );
        swarmNodeEntity.setCpuCores( form.getCpuCores() );
        swarmNodeEntity.setCreateTime( form.getCreateTime() );
        swarmNodeEntity.setDiskGb( form.getDiskGb() );
        swarmNodeEntity.setDockerVersion( form.getDockerVersion() );
        swarmNodeEntity.setHostname( form.getHostname() );
        swarmNodeEntity.setId( form.getId() );
        swarmNodeEntity.setIpAddress( form.getIpAddress() );
        swarmNodeEntity.setLabels( form.getLabels() );
        swarmNodeEntity.setLastHeartbeat( form.getLastHeartbeat() );
        swarmNodeEntity.setMemoryMb( form.getMemoryMb() );
        swarmNodeEntity.setNodeId( form.getNodeId() );
        swarmNodeEntity.setNodeName( form.getNodeName() );
        swarmNodeEntity.setOsType( form.getOsType() );
        swarmNodeEntity.setRole( form.getRole() );
        swarmNodeEntity.setStatus( form.getStatus() );
        swarmNodeEntity.setUpdateTime( form.getUpdateTime() );

        return swarmNodeEntity;
    }

    @Override
    public BusinessContainerRelForm toBusinessContainerRelForm(BusinessContainerRelEntity entity) {
        if ( entity == null ) {
            return null;
        }

        BusinessContainerRelForm businessContainerRelForm = new BusinessContainerRelForm();

        businessContainerRelForm.setAutoScaleEnabled( entity.getAutoScaleEnabled() );
        businessContainerRelForm.setBusinessId( entity.getBusinessId() );
        businessContainerRelForm.setContainerInstanceId( entity.getContainerInstanceId() );
        businessContainerRelForm.setCreateTime( entity.getCreateTime() );
        businessContainerRelForm.setCreatedBy( entity.getCreatedBy() );
        businessContainerRelForm.setDeploymentType( entity.getDeploymentType() );
        businessContainerRelForm.setHealthCheckEnabled( entity.getHealthCheckEnabled() );
        businessContainerRelForm.setId( entity.getId() );
        businessContainerRelForm.setPriority( entity.getPriority() );
        businessContainerRelForm.setUpdateTime( entity.getUpdateTime() );

        return businessContainerRelForm;
    }

    @Override
    public List<BusinessContainerRelForm> toBusinessContainerRelFormList(List<BusinessContainerRelEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<BusinessContainerRelForm> list = new ArrayList<BusinessContainerRelForm>( entities.size() );
        for ( BusinessContainerRelEntity businessContainerRelEntity : entities ) {
            list.add( toBusinessContainerRelForm( businessContainerRelEntity ) );
        }

        return list;
    }

    @Override
    public BusinessContainerRelEntity toBusinessContainerRelEntity(BusinessContainerRelForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessContainerRelEntity businessContainerRelEntity = new BusinessContainerRelEntity();

        businessContainerRelEntity.setAutoScaleEnabled( form.getAutoScaleEnabled() );
        businessContainerRelEntity.setBusinessId( form.getBusinessId() );
        businessContainerRelEntity.setContainerInstanceId( form.getContainerInstanceId() );
        businessContainerRelEntity.setCreateTime( form.getCreateTime() );
        businessContainerRelEntity.setCreatedBy( form.getCreatedBy() );
        businessContainerRelEntity.setDeploymentType( form.getDeploymentType() );
        businessContainerRelEntity.setHealthCheckEnabled( form.getHealthCheckEnabled() );
        businessContainerRelEntity.setId( form.getId() );
        businessContainerRelEntity.setPriority( form.getPriority() );
        businessContainerRelEntity.setUpdateTime( form.getUpdateTime() );

        return businessContainerRelEntity;
    }

    @Override
    public BusinessContainerDeploymentForm toBusinessContainerDeploymentForm(BusinessContainerDeploymentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        BusinessContainerDeploymentForm businessContainerDeploymentForm = new BusinessContainerDeploymentForm();

        businessContainerDeploymentForm.setBusinessId( entity.getBusinessId() );
        businessContainerDeploymentForm.setCreateTime( entity.getCreateTime() );
        businessContainerDeploymentForm.setCurrentReplicas( entity.getCurrentReplicas() );
        businessContainerDeploymentForm.setDeployCompleteTime( entity.getDeployCompleteTime() );
        businessContainerDeploymentForm.setDeployStartTime( entity.getDeployStartTime() );
        businessContainerDeploymentForm.setDeployedBy( entity.getDeployedBy() );
        businessContainerDeploymentForm.setDeploymentConfig( entity.getDeploymentConfig() );
        businessContainerDeploymentForm.setDeploymentName( entity.getDeploymentName() );
        businessContainerDeploymentForm.setDeploymentVersion( entity.getDeploymentVersion() );
        businessContainerDeploymentForm.setId( entity.getId() );
        businessContainerDeploymentForm.setImageId( entity.getImageId() );
        businessContainerDeploymentForm.setLastScaleTime( entity.getLastScaleTime() );
        businessContainerDeploymentForm.setRollbackVersion( entity.getRollbackVersion() );
        businessContainerDeploymentForm.setStatus( entity.getStatus() );
        businessContainerDeploymentForm.setTargetReplicas( entity.getTargetReplicas() );
        businessContainerDeploymentForm.setUpdateTime( entity.getUpdateTime() );

        return businessContainerDeploymentForm;
    }

    @Override
    public List<BusinessContainerDeploymentForm> toBusinessContainerDeploymentFormList(List<BusinessContainerDeploymentEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<BusinessContainerDeploymentForm> list = new ArrayList<BusinessContainerDeploymentForm>( entities.size() );
        for ( BusinessContainerDeploymentEntity businessContainerDeploymentEntity : entities ) {
            list.add( toBusinessContainerDeploymentForm( businessContainerDeploymentEntity ) );
        }

        return list;
    }

    @Override
    public BusinessContainerDeploymentEntity toBusinessContainerDeploymentEntity(BusinessContainerDeploymentForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessContainerDeploymentEntity businessContainerDeploymentEntity = new BusinessContainerDeploymentEntity();

        businessContainerDeploymentEntity.setBusinessId( form.getBusinessId() );
        businessContainerDeploymentEntity.setCreateTime( form.getCreateTime() );
        businessContainerDeploymentEntity.setCurrentReplicas( form.getCurrentReplicas() );
        businessContainerDeploymentEntity.setDeployCompleteTime( form.getDeployCompleteTime() );
        businessContainerDeploymentEntity.setDeployStartTime( form.getDeployStartTime() );
        businessContainerDeploymentEntity.setDeployedBy( form.getDeployedBy() );
        businessContainerDeploymentEntity.setDeploymentConfig( form.getDeploymentConfig() );
        businessContainerDeploymentEntity.setDeploymentName( form.getDeploymentName() );
        businessContainerDeploymentEntity.setDeploymentVersion( form.getDeploymentVersion() );
        businessContainerDeploymentEntity.setId( form.getId() );
        businessContainerDeploymentEntity.setImageId( form.getImageId() );
        businessContainerDeploymentEntity.setLastScaleTime( form.getLastScaleTime() );
        businessContainerDeploymentEntity.setRollbackVersion( form.getRollbackVersion() );
        businessContainerDeploymentEntity.setStatus( form.getStatus() );
        businessContainerDeploymentEntity.setTargetReplicas( form.getTargetReplicas() );
        businessContainerDeploymentEntity.setUpdateTime( form.getUpdateTime() );

        return businessContainerDeploymentEntity;
    }

    @Override
    public ApplicationContainerConfigForm toApplicationContainerConfigForm(ApplicationContainerConfigEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApplicationContainerConfigForm applicationContainerConfigForm = new ApplicationContainerConfigForm();

        applicationContainerConfigForm.setApplicationId( entity.getApplicationId() );
        applicationContainerConfigForm.setAutoScalingConfig( entity.getAutoScalingConfig() );
        applicationContainerConfigForm.setBackupStrategy( entity.getBackupStrategy() );
        applicationContainerConfigForm.setCreateTime( entity.getCreateTime() );
        applicationContainerConfigForm.setCreatedBy( entity.getCreatedBy() );
        applicationContainerConfigForm.setDefaultEnvironmentVars( entity.getDefaultEnvironmentVars() );
        applicationContainerConfigForm.setDefaultImageId( entity.getDefaultImageId() );
        applicationContainerConfigForm.setDefaultNodeSelector( entity.getDefaultNodeSelector() );
        applicationContainerConfigForm.setDefaultResourceLimits( entity.getDefaultResourceLimits() );
        applicationContainerConfigForm.setDeploymentStrategy( entity.getDeploymentStrategy() );
        applicationContainerConfigForm.setId( entity.getId() );
        applicationContainerConfigForm.setMaxInstancesPerBusiness( entity.getMaxInstancesPerBusiness() );
        applicationContainerConfigForm.setMonitoringConfig( entity.getMonitoringConfig() );
        applicationContainerConfigForm.setUpdateTime( entity.getUpdateTime() );

        return applicationContainerConfigForm;
    }

    @Override
    public List<ApplicationContainerConfigForm> toApplicationContainerConfigFormList(List<ApplicationContainerConfigEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ApplicationContainerConfigForm> list = new ArrayList<ApplicationContainerConfigForm>( entities.size() );
        for ( ApplicationContainerConfigEntity applicationContainerConfigEntity : entities ) {
            list.add( toApplicationContainerConfigForm( applicationContainerConfigEntity ) );
        }

        return list;
    }

    @Override
    public ApplicationContainerConfigEntity toApplicationContainerConfigEntity(ApplicationContainerConfigForm form) {
        if ( form == null ) {
            return null;
        }

        ApplicationContainerConfigEntity applicationContainerConfigEntity = new ApplicationContainerConfigEntity();

        applicationContainerConfigEntity.setApplicationId( form.getApplicationId() );
        applicationContainerConfigEntity.setAutoScalingConfig( form.getAutoScalingConfig() );
        applicationContainerConfigEntity.setBackupStrategy( form.getBackupStrategy() );
        applicationContainerConfigEntity.setCreateTime( form.getCreateTime() );
        applicationContainerConfigEntity.setCreatedBy( form.getCreatedBy() );
        applicationContainerConfigEntity.setDefaultEnvironmentVars( form.getDefaultEnvironmentVars() );
        applicationContainerConfigEntity.setDefaultImageId( form.getDefaultImageId() );
        applicationContainerConfigEntity.setDefaultNodeSelector( form.getDefaultNodeSelector() );
        applicationContainerConfigEntity.setDefaultResourceLimits( form.getDefaultResourceLimits() );
        applicationContainerConfigEntity.setDeploymentStrategy( form.getDeploymentStrategy() );
        applicationContainerConfigEntity.setId( form.getId() );
        applicationContainerConfigEntity.setMaxInstancesPerBusiness( form.getMaxInstancesPerBusiness() );
        applicationContainerConfigEntity.setMonitoringConfig( form.getMonitoringConfig() );
        applicationContainerConfigEntity.setUpdateTime( form.getUpdateTime() );

        return applicationContainerConfigEntity;
    }
}
