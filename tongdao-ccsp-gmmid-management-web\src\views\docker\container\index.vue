<template>
  <div class="app-container">
    <el-card shadow="never">
      <div class="filter-container clearfix">
        <el-form ref="searchForm" :model="listQuery" inline @submit.native.prevent="getList">
          <div class="filter-inner">
            <el-form-item label="容器名称">
              <el-input v-model.trim="listQuery.containerName" clearable placeholder="容器名称" />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="listQuery.status" clearable placeholder="请选择状态">
                <el-option label="运行中" value="running" />
                <el-option label="已停止" value="stopped" />
                <el-option label="已暂停" value="paused" />
              </el-select>
            </el-form-item>
            <el-form-item label="所属应用">
              <el-select v-model="listQuery.applicationId" clearable placeholder="请选择应用" filterable>
                <el-option
                  v-for="app in appOptions"
                  :key="app.value"
                  :label="app.label"
                  :value="app.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="设备类型">
              <el-select v-model="listQuery.deviceResourceType" clearable placeholder="请选择设备类型">
                <el-option label="VSM" value="VSM" />
                <el-option label="CHSM" value="CHSM" />
                <el-option label="HSM" value="HSM" />
              </el-select>
            </el-form-item>
          </div>
          <div class="pull-right">
            <el-button-group>
              <el-form-item>
                <el-button type="primary" @click="handleSearch"><i class="el-icon-search" /> 查询</el-button>
                <el-button type="info" @click="handleResetSearch"><i class="el-icon-refresh" /> 重置</el-button>
              </el-form-item>
            </el-button-group>
          </div>
        </el-form>
      </div>
    </el-card>
    
    <el-card shadow="never">
      <div class="filter-container clearfix">
        <div class="filter-inner">
          <el-button type="primary" @click="handleDeployContainer">部署容器</el-button>
          <el-button type="success" @click="handleSyncStatus">同步状态</el-button>
        </div>
      </div>
      
      <el-table
        ref="dataTable"
        v-loading="loading"
        :data="tableList"
        style="width: 100%;"
      >
        <el-table-column align="center" label="序号" type="index" width="50" />
        <el-table-column align="center" label="容器名称" min-width="150" prop="containerName" />
        <el-table-column align="center" label="所属应用" min-width="140">
          <template v-slot="{row}">
            <span>{{ row.applicationName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="镜像" min-width="150">
          <template v-slot="{row}">
            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" min-width="100" prop="status">
          <template v-slot="{row}">
            <el-tag v-if="row.status === 'running'" type="success">运行中</el-tag>
            <el-tag v-else-if="row.status === 'stopped'" type="danger">已停止</el-tag>
            <el-tag v-else-if="row.status === 'paused'" type="warning">已暂停</el-tag>
            <el-tag v-else>{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="端口" min-width="120">
          <template v-slot="{row}">
            <span>{{ formatPorts(row.portMappings) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="设备资源" min-width="140">
          <template v-slot="{row}">
            <div v-if="row.deviceResources && row.deviceResources.length > 0">
              <!-- 如果只有一个设备，显示详细信息 -->
              <div v-if="row.deviceResources.length === 1" class="device-resource-item">
                <el-tag :type="getDeviceTypeColor(row.deviceResources[0].deviceResourceType)" size="mini">
                  {{ row.deviceResources[0].deviceResourceType }}
                </el-tag>
                <div class="device-info-mini">
                  {{ row.deviceResources[0].deviceResourceName || row.deviceResources[0].deviceResourceId }}
                </div>
              </div>
              <!-- 如果有多个设备，显示紧凑格式 -->
              <div v-else class="device-resource-compact">
                <div class="device-tags">
                  <el-tag
                    v-for="device in row.deviceResources"
                    :key="device.id"
                    :type="getDeviceTypeColor(device.deviceResourceType)"
                    size="mini"
                    :title="`${device.deviceResourceType}: ${device.deviceResourceName || device.deviceResourceId}`"
                    style="margin: 1px;"
                  >
                    {{ device.deviceResourceType }}
                  </el-tag>
                </div>
                <div class="device-count-info">
                  共{{ row.deviceResources.length }}个设备
                </div>
              </div>
            </div>
            <div v-else-if="row.hsmDeviceId || row.hsmConfigured">
              <el-tag type="success" size="mini">HSM</el-tag>
              <div v-if="row.hsmDeviceName" class="device-info-mini">
                {{ row.hsmDeviceName }}
              </div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="访问地址" min-width="180">
          <template v-slot="{row}">
            <div v-if="row.accessUrl">
              <el-link :href="row.accessUrl" target="_blank" type="primary">
                {{ row.accessUrl }}
              </el-link>
              <el-button
                type="text"
                size="mini"
                @click="copyToClipboard(row.accessUrl)"
                style="margin-left: 5px;"
              >
                <i class="el-icon-copy-document"></i>
              </el-button>
            </div>
            <el-button
              v-else-if="row.status === 'running'"
              type="text"
              size="mini"
              @click="handleConfigRoute(row)"
            >
              配置访问
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="部署信息" min-width="140">
          <template v-slot="{row}">
            <div class="deploy-info">
              <div v-if="row.deployedByName" class="deploy-user">
                <i class="el-icon-user"></i> {{ row.deployedByName }}
              </div>
              <div v-if="row.createTime" class="deploy-time">
                <i class="el-icon-time"></i> {{ formatDate(row.createTime) }}
              </div>
              <div v-if="row.replicas && row.replicas > 1" class="deploy-replicas">
                <i class="el-icon-copy-document"></i> 副本: {{ row.replicas }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="250">
          <template v-slot="{row}">
            <el-button v-if="row.status === 'running'" type="text" @click="handleStopContainer(row)">停止</el-button>
            <el-button v-else type="text" @click="handleStartContainer(row)">启动</el-button>
            <el-button type="text" @click="handleRestartContainer(row)">重启</el-button>
            <el-button type="text" @click="handleScaleContainer(row)">扩缩容</el-button>
            <el-button type="text" @click="handleViewLogs(row)">日志</el-button>
            <el-button v-if="row.accessUrl" type="text" @click="handleUpdateRoute(row)">更新路由</el-button>
            <el-button type="text" @click="handleRemoveContainer(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > listQuery.pageSize"
        :limit.sync="listQuery.pageSize"
        :page.sync="listQuery.page"
        :total="total"
        layout="prev, pager, next"
        @pagination="getList"
      />
    </el-card>
    
    <!-- 部署容器对话框 -->
    <el-dialog :visible.sync="deployDialogVisible" title="部署容器" width="600px">
      <el-form ref="deployForm" :model="deployForm" :rules="deployRules" label-width="120px">
        <el-form-item label="关联应用" prop="applicationId">
          <el-select v-model="deployForm.applicationId" filterable placeholder="请选择应用" style="width: 100%;" :loading="appOptionsLoading">
            <el-option v-for="opt in appOptions" :key="opt.value" :label="opt.label" :value="String(opt.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="容器名称" prop="containerName">
          <el-input v-model.trim="deployForm.containerName" placeholder="例如: my-nginx" />
        </el-form-item>
        <el-form-item label="镜像选择" prop="imageId">
          <el-select 
            v-model="deployForm.imageId" 
            filterable 
            placeholder="请选择镜像"
            @change="handleImageChange"
            style="width: 100%;"
            :loading="imageListLoading"
            :disabled="imageList.length === 0"
          >
            <el-option
              v-for="image in imageList"
              :key="image.id"
              :label="`${image.name}:${image.tag}`"
              :value="image.id"
            >
              <span style="float: left">{{ image.name }}:{{ image.tag }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ formatImageSize(image.sizeMb) }}</span>
            </el-option>
            <div v-if="imageList.length === 0" slot="empty">
              <span v-if="imageListLoading">加载中...</span>
              <span v-else>暂无可用镜像，请先构建或导入镜像</span>
            </div>
          </el-select>
          <div class="form-help">
            选择已有的Docker镜像进行部署
            <el-button 
              type="text" 
              size="mini" 
              @click="loadImageList"
              style="margin-left: 8px;"
            >
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="镜像信息" v-if="selectedImage">
          <div class="image-info">
            <p><strong>名称：</strong>{{ selectedImage.name }}</p>
            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>
            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>
            <p v-if="selectedImage.description"><strong>描述：</strong>{{ selectedImage.description }}</p>
            <p><strong>服务端口：</strong>8300（Console服务固定端口）</p>
          </div>
        </el-form-item>
        <el-form-item label="副本数" prop="replicas">
          <el-input-number v-model="deployForm.replicas" :min="1" />
        </el-form-item>
        <el-form-item label="分布策略" v-if="deployForm.replicas > 1">
          <el-select v-model="deployForm.distributionStrategy" placeholder="请选择分布策略" style="width: 100%;">
            <el-option label="跨节点分散（推荐）" value="SPREAD_ACROSS_NODES" />
            <el-option label="仅Worker节点" value="WORKER_NODES_ONLY" />
            <el-option label="仅Manager节点" value="MANAGER_NODES_ONLY" />
            <el-option label="平衡分布" value="BALANCED" />
            <el-option label="跨可用区分散" value="SPREAD_ACROSS_ZONES" />
          </el-select>
          <div class="form-help">多副本时的部署分布策略，推荐选择跨节点分散</div>
        </el-form-item>

        <el-form-item label="设备资源配置">
          <el-switch v-model="deployForm.enableDeviceResource" active-text="启用" inactive-text="禁用" @change="handleDeviceResourceToggle" />
          <div class="form-help">启用后可配置各类设备资源（HSM、VSM、CHSM等）</div>
        </el-form-item>
        <el-form-item v-if="deployForm.enableDeviceResource" label="设备类型" prop="deviceResourceType">
          <el-select
            v-model="deployForm.deviceResourceType"
            placeholder="请选择设备类型"
            @change="handleDeviceTypeChange"
            style="width: 100%;"
          >
            <el-option label="VSM (虚拟密码机)" value="VSM" />
          </el-select>
          <div class="form-help">选择要使用的设备资源类型，当前只支持VSM虚拟密码机</div>
        </el-form-item>
        <el-form-item v-if="deployForm.enableDeviceResource && deployForm.deviceResourceType" label="分配类型" prop="allocationType">
          <el-radio-group v-model="deployForm.allocationType">
            <el-radio label="exclusive">独占模式</el-radio>
            <el-radio label="shared">共享模式</el-radio>
          </el-radio-group>
          <div class="form-help">
            独占模式：设备只能被当前容器使用；共享模式：设备可被多个容器共享使用
          </div>
        </el-form-item>
        <el-form-item v-if="deployForm.enableDeviceResource && deployForm.deviceResourceType" label="选择设备" prop="deviceResourceIds">
          <el-select
            v-model="deployForm.deviceResourceIds"
            multiple
            filterable
            :placeholder="`请选择${deployForm.deviceResourceType}设备`"
            @change="handleDeviceResourceChange"
            style="width: 100%;"
            :loading="deviceResourceListLoading"
            :disabled="availableDeviceResources.length === 0"
          >
            <el-option
              v-for="device in availableDeviceResources"
              :key="device.id"
              :label="device.name"
              :value="device.id"
              :disabled="!device.available"
            >
              <div class="device-option">
                <div class="device-option-main">
                  <span class="device-name">{{ device.name }}</span>
                  <el-tag
                    :type="device.available ? 'success' : 'danger'"
                    size="mini"
                    class="device-status"
                  >
                    {{ device.available ? '可用' : '不可用' }}
                  </el-tag>
                </div>
                <div class="device-option-detail">
                  <span class="device-address">{{ device.ipAddress }}:{{ device.port }}</span>
                  <span class="device-capacity" v-if="device.totalCapacity">
                    容量: {{ device.usedCapacity || 0 }}/{{ device.totalCapacity }}
                  </span>
                </div>
              </div>
            </el-option>
            <div v-if="availableDeviceResources.length === 0" slot="empty">
              <span v-if="deviceResourceListLoading">加载中...</span>
              <span v-else>暂无可用的{{ deployForm.deviceResourceType }}设备</span>
            </div>
          </el-select>
          <div class="form-help">
            选择可用的{{ deployForm.deviceResourceType }}设备资源
            <el-button
              type="text"
              size="mini"
              @click="loadDeviceResourceList"
              style="margin-left: 8px;"
            >
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="deployForm.enableDeviceResource && selectedDeviceResources.length > 0" label="设备信息">
          <div class="selected-devices">
            <div v-for="device in selectedDeviceResources" :key="device.id" class="device-info-card">
              <div class="device-card-header">
                <span class="device-card-name">{{ device.name }}</span>
                <el-tag :type="getDeviceTypeColor(device.type)" size="mini">{{ device.type }}</el-tag>
              </div>
              <div class="device-card-content">
                <p><strong>地址：</strong>{{ device.ipAddress }}:{{ device.port }}</p>
                <p v-if="device.managementPort"><strong>管理端口：</strong>{{ device.managementPort }}</p>
                <p><strong>状态：</strong>
                  <el-tag v-if="device.status === 'normal'" type="success" size="mini">正常</el-tag>
                  <el-tag v-else-if="device.status === 'running'" type="success" size="mini">运行中</el-tag>
                  <el-tag v-else type="warning" size="mini">{{ device.status }}</el-tag>
                </p>
                <p v-if="device.totalCapacity"><strong>容量：</strong>{{ device.usedCapacity || 0 }}/{{ device.totalCapacity }}</p>
                <p v-if="device.description"><strong>描述：</strong>{{ device.description }}</p>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deployDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDeployContainer">部署</el-button>
      </div>
    </el-dialog>
    
    <!-- 容器日志对话框 -->
    <el-dialog :visible.sync="logsDialogVisible" title="容器日志" width="800px">
      <el-input
        v-model="containerLogs"
        :rows="20"
        readonly
        type="textarea"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="logsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 扩缩容对话框 -->
    <el-dialog :visible.sync="scaleDialogVisible" title="容器扩缩容" width="500px">
      <el-form ref="scaleForm" :model="scaleForm" :rules="scaleRules" label-width="100px">
        <el-form-item label="副本数" prop="replicas">
          <el-input-number v-model="scaleForm.replicas" :min="1" />
          <div class="form-help">当前运行的容器实例数量</div>
        </el-form-item>
        <el-form-item label="分布策略" v-if="scaleForm.replicas > 1">
          <el-select v-model="scaleForm.distributionStrategy" placeholder="请选择分布策略" style="width: 100%;">
            <el-option label="跨节点分散（推荐）" value="SPREAD_ACROSS_NODES" />
            <el-option label="仅Worker节点" value="WORKER_NODES_ONLY" />
            <el-option label="仅Manager节点" value="MANAGER_NODES_ONLY" />
            <el-option label="平衡分布" value="BALANCED" />
          </el-select>
          <div class="form-help">多副本时的部署分布策略</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scaleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmScaleContainer">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import {
  getContainerList,
  deployContainer,
  getRecommendedStrategy,
  startContainer,
  stopContainer,
  restartContainer,
  removeContainer,
  getContainerLogs,
  syncContainerStatus,
  scaleContainer,
  updateContainerImage,
  configContainerRoute,
  updateContainerRoute,
  getAvailableDeviceResources,
  getContainerDeviceResources,
  getContainerAccessUrl
} from '@/api/docker/container'
import { getImageList } from '@/api/docker/image'
import { fetchApplicationOptions, fetchUserApps } from '@/api/application'
import { getAvailableHsmDevices, getHsmDeviceDetail } from '@/api/docker/hsm'

export default {
  name: 'DockerContainerIndex',
  components: {
    Pagination
  },
  data() {
    return {
      tableList: [],
      loading: false,
      total: 0,
      listQuery: {
        page: 1,
        pageSize: 20,
        containerName: undefined,
        status: undefined,
        applicationId: undefined,
        deviceResourceType: undefined
      },
      logsDialogVisible: false,
      containerLogs: '',
      currentContainer: null,
      deployDialogVisible: false,
      deployForm: {
        containerName: '',
        imageId: null,
        imageName: '',
        imageTag: 'latest',
        servicePort: 80,
        replicas: 1,
        distributionStrategy: 'SPREAD_ACROSS_NODES',
        enableHsm: false,
        hsmDeviceId: null,
        // 新的设备资源配置
        enableDeviceResource: false,
        deviceResourceType: '',
        deviceResourceIds: [],
        allocationType: 'exclusive',
        deployedBy: null,
        applicationId: null,
        applicationName: ''
      },
      deployRules: {
        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],
        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],
        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],
        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]
      },
      scaleDialogVisible: false,
      scaleForm: {
        id: '',
        replicas: 1,
        distributionStrategy: 'SPREAD_ACROSS_NODES'
      },
      scaleRules: {
        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]
      },
      // 镜像相关数据
      imageList: [],
      imageListLoading: false,
      selectedImage: null,
      // HSM设备相关数据
      hsmDeviceList: [],
      hsmDeviceListLoading: false,
      selectedHsmDevice: null,
      // 设备资源相关数据
      availableDeviceResources: [],
      deviceResourceListLoading: false,
      selectedDeviceResources: [],
      // 应用下拉
      appOptions: [],
      appOptionsLoading: false
    }
  },
  methods: {
    handleSearch() {
      this.getList()
    },
    handleResetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 20,
        containerName: undefined,
        status: undefined
      }
      this.getList()
    },
    handleDeployContainer() {
      // 首先加载镜像列表
      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {
        this.deployForm = {
          containerName: '',
          imageId: null,
          imageName: '',
          imageTag: 'latest',
          replicas: 1,
          distributionStrategy: 'SPREAD_ACROSS_NODES',
          enableHsm: false,
          hsmDeviceId: null,
          // 重置新的设备资源配置
          enableDeviceResource: false,
          deviceResourceType: '',
          deviceResourceIds: [],
          allocationType: 'exclusive',
          deployedBy: this.user ? this.user.id : null,
          applicationId: null,
          applicationName: ''
        }
        this.selectedImage = null
        this.selectedHsmDevice = null
        this.selectedDeviceResources = []
        this.availableDeviceResources = []
        this.deployDialogVisible = true
        this.$nextTick(() => {
          this.$refs['deployForm'].clearValidate()
        })
      })
    },
    confirmDeployContainer() {
      this.$refs['deployForm'].validate((valid) => {
        if (valid) {
          // 检查HSM设备配置
          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {
            this.$message.error('请选择HSM设备')
            return
          }

          // 检查设备资源配置
          if (this.deployForm.enableDeviceResource) {
            if (!this.deployForm.deviceResourceType) {
              this.$message.error('请选择设备资源类型')
              return
            }
            if (!this.deployForm.deviceResourceIds || this.deployForm.deviceResourceIds.length === 0) {
              this.$message.error('请选择设备资源')
              return
            }
          }
          
          const deployData = { ...this.deployForm }
          
          // 确保镜像信息完整
          if (this.selectedImage) {
            deployData.imageName = this.selectedImage.imageName
            deployData.imageTag = this.selectedImage.imageTag
          }
          
          // 构建HSM设备配置
          if (this.deployForm.enableHsm && this.selectedHsmDevice) {
            deployData.hsmDeviceConfig = {
              encryptorGroupId: 1, // 默认组ID
              encryptorId: this.selectedHsmDevice.deviceId,
              encryptorName: this.selectedHsmDevice.deviceName,
              serverIpAddr: this.selectedHsmDevice.ipAddress,
              serverPort: this.selectedHsmDevice.managementPort, // 使用管理端口8018
              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,
              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,
              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,
              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,
              dynamicLibPath: './libdeviceapi.so'
            }
          }
          
          // 不再使用Traefik配置，使用Docker Swarm内置负载均衡

          // 设置分布策略
          if (deployData.replicas > 1 && deployData.distributionStrategy) {
            deployData.distributionStrategy = deployData.distributionStrategy
          }

          // 新的设备资源配置（优先使用新的配置方式）
          if (this.deployForm.enableDeviceResource && this.deployForm.deviceResourceIds.length > 0) {
            deployData.deviceResourceConfig = {
              deviceResourceType: this.deployForm.deviceResourceType,
              deviceResourceIds: this.deployForm.deviceResourceIds,
              allocationType: this.deployForm.allocationType,
              priority: 1,
              configData: JSON.stringify({
                deviceType: this.deployForm.deviceResourceType,
                allocationType: this.deployForm.allocationType,
                selectedDevices: this.selectedDeviceResources.map(device => ({
                  id: device.id,
                  name: device.name,
                  type: device.type,
                  ipAddress: device.ipAddress,
                  port: device.port
                }))
              })
            }
          }
          // 兼容旧的HSM设备配置
          else if (this.deployForm.enableHsm && this.selectedHsmDevice && deployData.hsmDeviceConfig) {
            deployData.deviceResourceConfig = {
              deviceResourceType: 'VSM',
              deviceResourceIds: [this.selectedHsmDevice.deviceId],
              allocationType: 'exclusive',
              priority: 1,
              configData: JSON.stringify(deployData.hsmDeviceConfig)
            }
          }

          // 使用统一的部署接口
          console.log('使用统一的部署接口，配置：', {
            hsm: this.deployForm.enableHsm,
            strategy: deployData.distributionStrategy,
            replicas: deployData.replicas
          })

          deployContainer(deployData).then((response) => {
            this.deployDialogVisible = false
            if (response.code === 20000) {
              if (response.data) {
                // 处理不同的响应结构
                let message = '容器部署成功！'
                
                if (response.data.accessUrl) {
                  message += `访问地址：${response.data.accessUrl}`
                } else if (response.data.container && response.data.container.accessUrl) {
                  message += `访问地址：${response.data.container.accessUrl}`
                }
                
                if (response.data.distributionStrategy) {
                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`
                }
                
                if (response.data.hsmConfigured) {
                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`
                }
                
                this.$message.success(message)
              } else {
                this.$message.success('容器部署任务已提交')
              }
              this.getList()
            } else {
              this.$message.error(response.message || '部署失败')
            }
          }).catch(() => {
            // 错误处理已在拦截器中处理
          })
        }
      })
    },

    // 加载应用选项
    async loadAppOptions() {
      this.appOptionsLoading = true
      try {
        let resp
        // 租户角色使用 user/list-options，其它使用 fetch-options
        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {
          resp = await fetchUserApps()
        } else {
          resp = await fetchApplicationOptions()
        }
        if (resp && resp.code === 20000) {
          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []
          this.appOptions = data.map(item => {
            if (item.label && (item.value !== undefined)) return item
            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }
          })
        } else {
          this.appOptions = []
        }
      } catch (e) {
        this.appOptions = []
      } finally {
        this.appOptionsLoading = false
      }
    },
    handleStartContainer(row) {
      startContainer(row.id).then(() => {
        this.$message.success('容器启动成功')
        this.getList()
      })
    },
    handleStopContainer(row) {
      stopContainer(row.id).then(() => {
        this.$message.success('容器停止成功')
        this.getList()
      })
    },
    handleRestartContainer(row) {
      restartContainer(row.id).then(() => {
        this.$message.success('容器重启成功')
        this.getList()
      })
    },
    handleRemoveContainer(row) {
      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeContainer(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      }).catch(() => {
        // 取消删除
      })
    },
    handleScaleContainer(row) {
      this.scaleForm = {
        id: row.id,
        replicas: row.replicas || 1, // 使用当前副本数或默认值
        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'
      }
      this.scaleDialogVisible = true
      this.$nextTick(() => {
        this.$refs['scaleForm'].clearValidate()
      })
    },
    confirmScaleContainer() {
      this.$refs['scaleForm'].validate((valid) => {
        if (valid) {
          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {
            this.scaleDialogVisible = false
            this.$message.success('容器扩缩容任务已提交')
            this.getList()
          })
        }
      })
    },
    handleViewLogs(row) {
      this.currentContainer = row
      getContainerLogs(row.id).then(response => {
        if (response.code === 20000) {
          this.containerLogs = response.data || ''
          this.logsDialogVisible = true
        }
      })
    },
    handleSyncStatus() {
      syncContainerStatus().then(() => {
        this.$message.success('容器状态同步任务已提交')
        this.getList()
      })
    },
    getList() {
      this.loading = true
      getContainerList(this.listQuery).then(response => {
        if (response.code === 20000) {
          this.tableList = response.data.list || []
          this.total = response.data.totalCount || 0

          // 为每个容器加载设备资源信息
          this.loadContainerDeviceResources()
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 加载容器设备资源信息
    async loadContainerDeviceResources() {
      if (!this.tableList || this.tableList.length === 0) {
        return
      }

      // 并发加载所有容器的设备资源信息
      const promises = this.tableList.map(async (container) => {
        try {
          const response = await getContainerDeviceResources(container.id)
          if (response.code === 20000 && response.data) {
            // 处理不同的数据结构
            let deviceResources = []
            if (Array.isArray(response.data)) {
              // 直接数组结构
              deviceResources = response.data
            } else if (response.data.list && Array.isArray(response.data.list)) {
              // 分页结构：{totalCount: 1, list: [...]}
              deviceResources = response.data.list
            }
            // 将设备资源信息添加到容器对象中
            this.$set(container, 'deviceResources', deviceResources)
          }
        } catch (error) {
          console.warn(`获取容器${container.id}的设备资源信息失败:`, error)
        }
      })

      await Promise.all(promises)
    },
    // 格式化端口映射显示
    formatPorts(portMappings) {
      if (!portMappings) return '-'
      try {
        const ports = JSON.parse(portMappings)
        if (Array.isArray(ports) && ports.length > 0) {
          // return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')
          return ports.map(p => `${p.hostPort || ''}/${p.protocol || 'tcp'}`).join(', ')
        }
        return '-'
      } catch (e) {
        return portMappings
      }
    },
    // 格式化日期显示
    formatDate(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    
    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('地址已复制到剪贴板')
        })
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('地址已复制到剪贴板')
      }
    },
    
    // 配置访问路由
    async handleConfigRoute(row) {
      try {
        // 先获取容器的访问地址信息
        const response = await getContainerAccessUrl(row.id)
        if (response.code === 20000 && response.data) {
          const { accessPort, accessInfo } = response.data

          if (accessPort && accessPort > 0) {
            // 如果已有分配的端口，显示访问信息
            this.$alert(
              `${accessInfo}\n\n当前使用Docker Swarm内置负载均衡，无需额外配置路由。`,
              '容器访问信息',
              {
                confirmButtonText: '确定',
                type: 'info'
              }
            )
          } else {
            // 如果没有分配端口，提示用户
            this.$alert(
              '该容器尚未分配外部访问端口。\n\n请确保容器部署时配置了端口映射，或联系管理员分配端口。',
              '无访问端口',
              {
                confirmButtonText: '确定',
                type: 'warning'
              }
            )
          }
        } else {
          this.$message.error('获取容器访问信息失败：' + (response.message || '未知错误'))
        }
      } catch (error) {
        console.error('获取容器访问信息失败:', error)
        this.$message.error('获取容器访问信息失败')
      }
    },
    
    // 更新路由配置
    handleUpdateRoute(row) {
      this.$prompt('请输入新的服务端口', '更新路由', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          const port = parseInt(value)
          if (!port || port < 1 || port > 65535) {
            return '请输入有效的端口号(1-65535)'
          }
          return true
        }
      }).then(({ value }) => {
        const routeConfig = {
          containerId: row.id,
          servicePort: parseInt(value)
        }
        
        this.updateContainerRoute(routeConfig).then((response) => {
          if (response.code === 20000 && response.data && response.data.accessUrl) {
            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)
            this.getList()
          } else {
            this.$message.error(response.message || '更新失败')
          }
        }).catch(() => {
          // 错误处理已在拦截器中处理
        })
      }).catch(() => {
        // 取消操作
      })
    },
    

    
    // 配置容器路由方法
    async configContainerRoute(routeConfig) {
      return configContainerRoute(routeConfig)
    },
    
    // 更新容器路由方法
    async updateContainerRoute(routeConfig) {
      return updateContainerRoute(routeConfig)
    },
    
    // 加载镜像列表
    async loadImageList() {
      this.imageListLoading = true
      try {
        const response = await getImageList()
        if (response.code === 20000) {
          // 处理分页数据结构
          if (response.data && response.data.list) {
            // 如果返回的是分页结构
            this.imageList = response.data.list.map(image => {
              return {
                ...image,
                // 确保 sizeMb 是数字类型
                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb
              }
            })
          } else if (Array.isArray(response.data)) {
            // 如果返回的是直接数组
            this.imageList = response.data.map(image => {
              return {
                ...image,
                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb
              }
            })
          } else {
            this.imageList = []
          }
          
          if (this.imageList.length === 0) {
            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')
          }
        } else {
          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))
          this.imageList = []
        }
      } catch (error) {
        console.error('加载镜像列表失败:', error)
        this.$message.error('加载镜像列表失败')
        this.imageList = []
      } finally {
        this.imageListLoading = false
      }
    },
    
    // 处理镜像选择变化
    handleImageChange(imageId) {
      if (imageId) {
        this.selectedImage = this.imageList.find(img => img.id === imageId)
        if (this.selectedImage) {
          // 自动填充镜像名称和标签
          this.deployForm.imageName = this.selectedImage.name
          this.deployForm.imageTag = this.selectedImage.tag
          
          // 智能生成容器名称（如果当前为空）
          if (!this.deployForm.containerName) {
            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)
          }
          
          // 根据镜像类型智能推荐端口
          this.suggestServicePort(this.selectedImage.name)
        }
      } else {
        this.selectedImage = null
        this.deployForm.imageName = ''
        this.deployForm.imageTag = 'latest'
      }
    },
    
    // 智能生成容器名称
    suggestContainerName(imageName, imageTag) {
      // 移除镜像名称中的特殊字符，生成简洁的名称
      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()
      
      // 如果标签不是latest，则加入标签
      if (imageTag && imageTag !== 'latest') {
        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')
      }
      
      // 添加时间戳保证唯一性
      const timestamp = Date.now().toString().slice(-6)
      this.deployForm.containerName = `${baseName}-${timestamp}`
    },
    
    // 根据镜像类型智能推荐端口
    suggestServicePort(imageName) {
      const portMap = {
        'nginx': 80,
        'apache': 80,
        'httpd': 80,
        'mysql': 3306,
        'mariadb': 3306,
        'postgres': 5432,
        'postgresql': 5432,
        'redis': 6379,
        'mongodb': 27017,
        'mongo': 27017,
        'tomcat': 8080,
        'node': 3000,
        'spring': 8080,
        'java': 8080
      }
      
      // 查找匹配的镜像类型
      for (const [key, port] of Object.entries(portMap)) {
        if (imageName.toLowerCase().includes(key)) {
          this.deployForm.servicePort = port
          break
        }
      }
    },
    
    // 格式化镜像大小
    formatImageSize(sizeMb) {
      if (!sizeMb || sizeMb === 0) return '-'

      // 确保是数字类型
      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb
      if (isNaN(size)) return '-'

      // 如果已经是MB单位，直接显示
      if (size < 1024) {
        return `${size} MB`
      }

      // 转换为GB
      const sizeGb = size / 1024
      return `${sizeGb.toFixed(1)} GB`
    },
    
    // 获取分布策略显示名称
    getStrategyDisplayName(strategy) {
      const strategyMap = {
        'SPREAD_ACROSS_NODES': '跨节点分散',
        'WORKER_NODES_ONLY': '仅Worker节点',
        'MANAGER_NODES_ONLY': '仅Manager节点',
        'BALANCED': '平衡分布',
        'SPREAD_ACROSS_ZONES': '跨可用区分散'
      }
      return strategyMap[strategy] || strategy
    },
    
    // 获取推荐的分布策略
    async getRecommendedDistributionStrategy(replicas) {
      try {
        const response = await getRecommendedStrategy(replicas)
        if (response.code === 20000 && response.data) {
          return response.data.recommendedStrategy
        }
      } catch (error) {
        console.warn('获取推荐分布策略失败:', error)
      }
      return 'SPREAD_ACROSS_NODES' // 默认值
    },

    // === 设备资源相关方法 ===

    // 处理设备资源开关变化
    handleDeviceResourceToggle(enabled) {
      if (enabled) {
        // 启用设备资源时自动选择VSM并加载设备列表
        this.deployForm.deviceResourceType = 'VSM'
        this.deployForm.deviceResourceIds = []
        this.deployForm.allocationType = 'exclusive'
        this.selectedDeviceResources = []
        this.availableDeviceResources = []
        // 自动加载VSM设备列表
        this.loadDeviceResourceList()
      } else {
        // 禁用设备资源时清空所有相关配置
        this.deployForm.deviceResourceType = ''
        this.deployForm.deviceResourceIds = []
        this.selectedDeviceResources = []
        this.availableDeviceResources = []
      }
    },

    // 处理设备类型变化
    handleDeviceTypeChange(deviceType) {
      if (deviceType) {
        // 切换设备类型时清空已选择的设备
        this.deployForm.deviceResourceIds = []
        this.selectedDeviceResources = []
        // 加载对应类型的设备列表
        this.loadDeviceResourceList()
      } else {
        this.availableDeviceResources = []
        this.selectedDeviceResources = []
      }
    },

    // 加载设备资源列表
    async loadDeviceResourceList() {
      if (!this.deployForm.deviceResourceType) {
        return
      }

      this.deviceResourceListLoading = true
      try {
        const response = await getAvailableDeviceResources({
          deviceResourceType: this.deployForm.deviceResourceType,
          excludeContainerId: null, // 新部署时不需要排除
          allocationType: this.deployForm.allocationType // 传递用户选择的分配类型
        })

        console.log('设备资源API返回:', response) // 调试日志

        if (response.code === 20000) {
          // 处理不同的数据结构
          let deviceList = []
          if (response.data && Array.isArray(response.data.list)) {
            // 分页结构：{totalCount: 12, list: [...]}
            deviceList = response.data.list
          } else if (Array.isArray(response.data)) {
            // 直接数组结构
            deviceList = response.data
          } else {
            deviceList = []
          }

          this.availableDeviceResources = deviceList

          if (this.availableDeviceResources.length === 0) {
            this.$message.info(`当前没有可用的${this.deployForm.deviceResourceType}设备`)
          } else {
            console.log('加载到设备资源:', this.availableDeviceResources.length, '个')
          }
        } else {
          this.$message.error('获取设备资源列表失败：' + (response.message || '未知错误'))
          this.availableDeviceResources = []
        }
      } catch (error) {
        console.error('加载设备资源列表失败:', error)
        this.$message.error('加载设备资源列表失败')
        this.availableDeviceResources = []
      } finally {
        this.deviceResourceListLoading = false
      }
    },

    // 处理设备资源选择变化
    handleDeviceResourceChange(deviceIds) {
      this.selectedDeviceResources = []
      if (deviceIds && deviceIds.length > 0) {
        deviceIds.forEach(deviceId => {
          const device = this.availableDeviceResources.find(d => d.id === deviceId)
          if (device) {
            this.selectedDeviceResources.push(device)
          }
        })
      }
    },

    // 获取设备类型颜色
    getDeviceTypeColor(deviceType) {
      const colorMap = {
        'VSM': 'success',
        'CHSM': 'primary',
        'HSM': 'warning'
      }
      return colorMap[deviceType] || 'info'
    },

    // === HSM设备相关方法 ===
    
    // 处理HSM开关变化
    handleHsmToggle(enabled) {
      if (enabled) {
        // 启用HSM时加载设备列表
        this.loadHsmDeviceList()
      } else {
        // 禁用HSM时清空选择
        this.deployForm.hsmDeviceId = null
        this.selectedHsmDevice = null
      }
    },
    
    // 加载HSM设备列表
    async loadHsmDeviceList() {
      this.hsmDeviceListLoading = true
      try {
        const response = await getAvailableHsmDevices({
          status: 'running'
        })
        
        if (response.code === 20000) {
          if (response.data && response.data.list) {
            this.hsmDeviceList = response.data.list
          } else if (Array.isArray(response.data)) {
            this.hsmDeviceList = response.data
          } else {
            this.hsmDeviceList = []
          }
          
          if (this.hsmDeviceList.length === 0) {
            this.$message.info('当前没有可用的HSM设备')
          }
        } else {
          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))
          this.hsmDeviceList = []
        }
      } catch (error) {
        console.error('加载HSM设备列表失败:', error)
        this.$message.error('加载HSM设备列表失败')
        this.hsmDeviceList = []
      } finally {
        this.hsmDeviceListLoading = false
      }
    },
    
    // 处理HSM设备选择变化
    handleHsmDeviceChange(deviceId) {
      if (deviceId) {
        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)
        if (this.selectedHsmDevice) {
          // 可以在这里加载更详细的设备信息
          this.loadHsmDeviceDetail(deviceId)
        }
      } else {
        this.selectedHsmDevice = null
      }
    },
    
    // 加载HSM设备详细信息
    async loadHsmDeviceDetail(deviceId) {
      try {
        const response = await getHsmDeviceDetail(deviceId)
        if (response.code === 20000 && response.data) {
          this.selectedHsmDevice = response.data
        }
      } catch (error) {
        console.warn('获取HSM设备详细信息失败:', error)
      }
    },
    

  },
  mounted() {
    this.getList()
    // 预加载镜像列表，提升用户体验
    this.loadImageList()
    // 预加载HSM设备列表
    this.loadHsmDeviceList()
    // 预加载应用选项
    this.loadAppOptions()
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    // 监听副本数变化，自动更新推荐的分布策略
    'deployForm.replicas'(newReplicas, oldReplicas) {
      if (newReplicas > 1 && newReplicas !== oldReplicas) {
        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {
          this.deployForm.distributionStrategy = strategy
        })
      } else if (newReplicas === 1) {
        // 单副本时不需要分布策略
        this.deployForm.distributionStrategy = 'BALANCED'
      }
    },
    // 监听扩缩容副本数变化
    'scaleForm.replicas'(newReplicas, oldReplicas) {
      if (newReplicas > 1 && newReplicas !== oldReplicas) {
        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {
          this.scaleForm.distributionStrategy = strategy
        })
      } else if (newReplicas === 1) {
        this.scaleForm.distributionStrategy = 'BALANCED'
      }
    },
    // 监听分配类型变化，重新加载设备列表
    'deployForm.allocationType'(newAllocationType, oldAllocationType) {
      if (newAllocationType !== oldAllocationType && this.deployForm.deviceResourceType) {
        // 清空已选择的设备
        this.deployForm.deviceResourceIds = []
        this.selectedDeviceResources = []
        // 重新加载设备列表
        this.loadDeviceResourceList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-inner {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  
  .el-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
  }
}

.pull-right {
  float: right;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.el-link {
  font-size: 12px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.image-info {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  
  p {
    margin: 4px 0;
    font-size: 13px;
    
    strong {
      color: #303133;
      font-weight: 500;
    }
  }
}

.device-info {
  background-color: #f0f9ff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #a7f3d0;
  
  p {
    margin: 4px 0;
    font-size: 13px;
    
    strong {
      color: #303133;
      font-weight: 500;
    }
  }
}

.strategy-info {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.hsm-info {
  font-size: 11px;
  color: #67c23a;
  margin-top: 2px;
}

// 设备资源相关样式
.device-resource-item {
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.device-info-mini {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.device-option {
  width: 100%;

  .device-option-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;

    .device-name {
      font-weight: 500;
    }

    .device-status {
      margin-left: 8px;
    }
  }

  .device-option-detail {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #8492a6;

    .device-address {
      flex: 1;
    }

    .device-capacity {
      margin-left: 8px;
    }
  }
}

.selected-devices {
  max-height: 300px;
  overflow-y: auto;
}

.device-info-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  .device-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .device-card-name {
      font-weight: 600;
      color: #303133;
    }
  }

  .device-card-content {
    p {
      margin: 4px 0;
      font-size: 13px;
      color: #606266;

      strong {
        color: #303133;
      }
    }
  }
}

// 规格信息样式
.spec-info {
  font-size: 12px;

  .resource-limit {
    color: #909399;
    margin-top: 2px;

    span {
      margin-right: 8px;
    }
  }
}

// 设备资源显示样式
.device-resource-compact {
  .device-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    margin-bottom: 2px;
  }

  .device-count-info {
    font-size: 11px;
    color: #909399;
    text-align: center;
  }
}

// 部署信息样式
.deploy-info {
  font-size: 12px;

  .deploy-user,
  .deploy-time,
  .deploy-node {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
    color: #606266;

    i {
      margin-right: 4px;
      width: 12px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>