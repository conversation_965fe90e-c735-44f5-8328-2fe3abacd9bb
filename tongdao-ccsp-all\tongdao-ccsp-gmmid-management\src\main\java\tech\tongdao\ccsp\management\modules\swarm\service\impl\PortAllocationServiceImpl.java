package tech.tongdao.ccsp.management.modules.swarm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tongdao.ccsp.management.modules.swarm.entity.ContainerPortAllocationEntity;
import tech.tongdao.ccsp.management.modules.swarm.mapper.ContainerPortAllocationMapper;
import tech.tongdao.ccsp.management.modules.swarm.service.PortAllocationService;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 端口分配服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class PortAllocationServiceImpl extends ServiceImpl<ContainerPortAllocationMapper, ContainerPortAllocationEntity>
        implements PortAllocationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PortAllocationServiceImpl.class);

    /**
     * 端口池起始端口
     */
    @Value("${ccsp.port-pool.start:30000}")
    private Integer portPoolStart;

    /**
     * 端口池结束端口
     */
    @Value("${ccsp.port-pool.end:32767}")
    private Integer portPoolEnd;

    /**
     * 内存中的端口分配缓存，用于快速检查端口可用性
     */
    private final Map<Integer, Long> portCache = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer allocatePort(Long containerInstanceId, Long userId, Integer preferredPort) {
        return allocatePortWithDetails(containerInstanceId, null, null, userId, null, preferredPort);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer allocatePortWithDetails(Long containerInstanceId, String containerName, Integer servicePort,
                                          Long userId, String username, Integer preferredPort) {
        try {
            LOGGER.info("为容器分配端口，容器ID：{}，容器名：{}，内部端口：{}，用户ID：{}，首选端口：{}",
                    containerInstanceId, containerName, servicePort, userId, preferredPort);

            // 检查容器是否已经分配了端口
            Integer existingPort = getContainerPort(containerInstanceId);
            if (existingPort != null) {
                LOGGER.info("容器{}已分配端口：{}", containerInstanceId, existingPort);
                return existingPort;
            }

            Integer allocatedPort = null;

            // 如果指定了首选端口，先尝试分配首选端口
            if (preferredPort != null && isPortInRange(preferredPort) && isPortAvailable(preferredPort)) {
                allocatedPort = preferredPort;
            } else {
                // 查找可用端口
                allocatedPort = findAvailablePort();
            }

            if (allocatedPort == null) {
                LOGGER.error("无法为容器{}分配端口，端口池已满", containerInstanceId);
                return null;
            }

            // 创建端口分配记录
            ContainerPortAllocationEntity entity = new ContainerPortAllocationEntity();
            entity.setContainerInstanceId(containerInstanceId);
            entity.setContainerName(containerName);
            entity.setAllocatedPort(allocatedPort);
            entity.setServicePort(servicePort);
            entity.setUserId(userId);
            entity.setUsername(username);
            entity.setStatus("active");
            entity.setAllocatedAt(LocalDateTime.now());
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreatedBy(userId);

            boolean saved = this.save(entity);
            if (saved) {
                // 更新缓存
                portCache.put(allocatedPort, containerInstanceId);
                LOGGER.info("成功为容器{}分配端口：{}，内部端口：{}", containerInstanceId, allocatedPort, servicePort);
                return allocatedPort;
            } else {
                LOGGER.error("保存端口分配记录失败，容器ID：{}，端口：{}", containerInstanceId, allocatedPort);
                return null;
            }

        } catch (Exception e) {
            LOGGER.error("分配端口失败", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releasePort(Long containerInstanceId) {
        try {
            LOGGER.info("释放容器端口，容器ID：{}", containerInstanceId);

            LambdaUpdateWrapper<ContainerPortAllocationEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ContainerPortAllocationEntity::getContainerInstanceId, containerInstanceId)
                    .eq(ContainerPortAllocationEntity::getStatus, "active")
                    .set(ContainerPortAllocationEntity::getStatus, "released")
                    .set(ContainerPortAllocationEntity::getReleasedAt, LocalDateTime.now())
                    .set(ContainerPortAllocationEntity::getUpdateTime, LocalDateTime.now());

            boolean updated = this.update(updateWrapper);
            if (updated) {
                // 从缓存中移除
                Integer port = getContainerPort(containerInstanceId);
                if (port != null) {
                    portCache.remove(port);
                }
                LOGGER.info("成功释放容器{}的端口", containerInstanceId);
                return true;
            } else {
                LOGGER.warn("未找到容器{}的活跃端口分配记录", containerInstanceId);
                return false;
            }

        } catch (Exception e) {
            LOGGER.error("释放端口失败", e);
            return false;
        }
    }

    @Override
    public boolean isPortAvailable(Integer port) {
        if (!isPortInRange(port)) {
            return false;
        }

        // 先检查缓存
        if (portCache.containsKey(port)) {
            return false;
        }

        // 检查数据库
        LambdaQueryWrapper<ContainerPortAllocationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContainerPortAllocationEntity::getAllocatedPort, port)
                .eq(ContainerPortAllocationEntity::getStatus, "active");

        return this.count(queryWrapper) == 0;
    }

    @Override
    public Integer getContainerPort(Long containerInstanceId) {
        LambdaQueryWrapper<ContainerPortAllocationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContainerPortAllocationEntity::getContainerInstanceId, containerInstanceId)
                .eq(ContainerPortAllocationEntity::getStatus, "active")
                .last("LIMIT 1");

        ContainerPortAllocationEntity entity = this.getOne(queryWrapper);
        return entity != null ? entity.getAllocatedPort() : null;
    }

    @Override
    public List<Integer> getUserPorts(Long userId) {
        return baseMapper.findUserActivePorts(userId);
    }

    @Override
    public Map<String, Object> getPortPoolStats() {
        Map<String, Object> stats = baseMapper.getPortPoolStats(portPoolStart, portPoolEnd);
        
        int totalPorts = portPoolEnd - portPoolStart + 1;
        int usedPorts = stats.get("used_ports") != null ? 
                ((Number) stats.get("used_ports")).intValue() : 0;
        int availablePorts = totalPorts - usedPorts;
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalPorts", totalPorts);
        result.put("usedPorts", usedPorts);
        result.put("availablePorts", availablePorts);
        result.put("usageRate", totalPorts > 0 ? (double) usedPorts / totalPorts * 100 : 0);
        result.put("portRangeStart", portPoolStart);
        result.put("portRangeEnd", portPoolEnd);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recycleUnusedPorts(int unusedDays) {
        LocalDateTime thresholdTime = LocalDateTime.now().minusDays(unusedDays);
        List<ContainerPortAllocationEntity> unusedAllocations = 
                baseMapper.findUnusedAllocations(thresholdTime);

        if (unusedAllocations.isEmpty()) {
            LOGGER.info("没有找到需要回收的端口");
            return 0;
        }

        int recycledCount = 0;
        for (ContainerPortAllocationEntity allocation : unusedAllocations) {
            if (releasePort(allocation.getContainerInstanceId())) {
                recycledCount++;
            }
        }

        LOGGER.info("回收了{}个长时间未使用的端口", recycledCount);
        return recycledCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContainerId(Long oldContainerId, Long newContainerId) {
        try {
            LOGGER.info("更新端口分配记录的容器ID，旧ID：{}，新ID：{}", oldContainerId, newContainerId);

            LambdaUpdateWrapper<ContainerPortAllocationEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ContainerPortAllocationEntity::getContainerInstanceId, oldContainerId)
                    .eq(ContainerPortAllocationEntity::getStatus, "active")
                    .set(ContainerPortAllocationEntity::getContainerInstanceId, newContainerId)
                    .set(ContainerPortAllocationEntity::getUpdateTime, LocalDateTime.now());

            boolean updated = this.update(updateWrapper);
            if (updated) {
                // 更新缓存
                Integer port = getContainerPort(newContainerId);
                if (port != null) {
                    portCache.remove(port);
                    portCache.put(port, newContainerId);
                }
                LOGGER.info("成功更新端口分配记录的容器ID，旧ID：{}，新ID：{}", oldContainerId, newContainerId);
                return true;
            } else {
                LOGGER.warn("未找到容器{}的端口分配记录", oldContainerId);
                return false;
            }

        } catch (Exception e) {
            LOGGER.error("更新端口分配记录的容器ID失败", e);
            return false;
        }
    }

    @Override
    public Integer getPortByContainerName(String containerName) {
        try {
            LOGGER.info("通过容器名称查找分配的端口，容器名称：{}", containerName);

            LambdaQueryWrapper<ContainerPortAllocationEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ContainerPortAllocationEntity::getContainerName, containerName)
                    .eq(ContainerPortAllocationEntity::getStatus, "active")
                    .last("LIMIT 1");

            ContainerPortAllocationEntity entity = this.getOne(queryWrapper);
            Integer port = entity != null ? entity.getAllocatedPort() : null;

            LOGGER.info("通过容器名称{}查找到端口：{}", containerName, port);
            return port;

        } catch (Exception e) {
            LOGGER.error("通过容器名称查找端口失败，容器名称：{}", containerName, e);
            return null;
        }
    }

    /**
     * 检查端口是否在允许的范围内
     */
    private boolean isPortInRange(Integer port) {
        return port != null && port >= portPoolStart && port <= portPoolEnd;
    }

    /**
     * 查找可用端口
     */
    private Integer findAvailablePort() {
        // 获取已分配的端口列表
        List<Integer> allocatedPorts = baseMapper.findAllocatedPortsInRange(portPoolStart, portPoolEnd);
        Set<Integer> allocatedSet = new HashSet<>(allocatedPorts);

        // 从起始端口开始查找第一个可用端口
        for (int port = portPoolStart; port <= portPoolEnd; port++) {
            if (!allocatedSet.contains(port)) {
                return port;
            }
        }

        return null; // 端口池已满
    }
}
