{"remainingRequest": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\src\\views\\docker\\container\\index.vue", "mtime": 1757058863660}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1729062151198}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1729062151209}, {"path": "D:\\workspace\\ccsp_v3\\tongdao-ccsp-gmmid-management-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1729062152627}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicKaW1wb3J0IHsKICBnZXRDb250YWluZXJMaXN0LAogIGRlcGxveUNvbnRhaW5lciwKICBnZXRSZWNvbW1lbmRlZFN0cmF0ZWd5LAogIHN0YXJ0Q29udGFpbmVyLAogIHN0b3BDb250YWluZXIsCiAgcmVzdGFydENvbnRhaW5lciwKICByZW1vdmVDb250YWluZXIsCiAgZ2V0Q29udGFpbmVyTG9ncywKICBzeW5jQ29udGFpbmVyU3RhdHVzLAogIHNjYWxlQ29udGFpbmVyLAogIHVwZGF0ZUNvbnRhaW5lckltYWdlLAogIGNvbmZpZ0NvbnRhaW5lclJvdXRlLAogIHVwZGF0ZUNvbnRhaW5lclJvdXRlLAogIGdldEF2YWlsYWJsZURldmljZVJlc291cmNlcywKICBnZXRDb250YWluZXJEZXZpY2VSZXNvdXJjZXMsCiAgZ2V0Q29udGFpbmVyQWNjZXNzVXJsCn0gZnJvbSAnQC9hcGkvZG9ja2VyL2NvbnRhaW5lcicKaW1wb3J0IHsgZ2V0SW1hZ2VMaXN0IH0gZnJvbSAnQC9hcGkvZG9ja2VyL2ltYWdlJwppbXBvcnQgeyBmZXRjaEFwcGxpY2F0aW9uT3B0aW9ucywgZmV0Y2hVc2VyQXBwcyB9IGZyb20gJ0AvYXBpL2FwcGxpY2F0aW9uJwppbXBvcnQgeyBnZXRBdmFpbGFibGVIc21EZXZpY2VzLCBnZXRIc21EZXZpY2VEZXRhaWwgfSBmcm9tICdAL2FwaS9kb2NrZXIvaHNtJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEb2NrZXJDb250YWluZXJJbmRleCcsCiAgY29tcG9uZW50czogewogICAgUGFnaW5hdGlvbgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRhYmxlTGlzdDogW10sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdFF1ZXJ5OiB7CiAgICAgICAgcGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgY29udGFpbmVyTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLAogICAgICAgIGFwcGxpY2F0aW9uSWQ6IHVuZGVmaW5lZCwKICAgICAgICBkZXZpY2VSZXNvdXJjZVR5cGU6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBsb2dzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGNvbnRhaW5lckxvZ3M6ICcnLAogICAgICBjdXJyZW50Q29udGFpbmVyOiBudWxsLAogICAgICBkZXBsb3lEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGVwbG95Rm9ybTogewogICAgICAgIGNvbnRhaW5lck5hbWU6ICcnLAogICAgICAgIGltYWdlSWQ6IG51bGwsCiAgICAgICAgaW1hZ2VOYW1lOiAnJywKICAgICAgICBpbWFnZVRhZzogJ2xhdGVzdCcsCiAgICAgICAgc2VydmljZVBvcnQ6IDgwLAogICAgICAgIHJlcGxpY2FzOiAxLAogICAgICAgIGRpc3RyaWJ1dGlvblN0cmF0ZWd5OiAnU1BSRUFEX0FDUk9TU19OT0RFUycsCiAgICAgICAgZW5hYmxlSHNtOiBmYWxzZSwKICAgICAgICBoc21EZXZpY2VJZDogbnVsbCwKICAgICAgICAvLyDmlrDnmoTorr7lpIfotYTmupDphY3nva4KICAgICAgICBlbmFibGVEZXZpY2VSZXNvdXJjZTogZmFsc2UsCiAgICAgICAgZGV2aWNlUmVzb3VyY2VUeXBlOiAnJywKICAgICAgICBkZXZpY2VSZXNvdXJjZUlkczogW10sCiAgICAgICAgYWxsb2NhdGlvblR5cGU6ICdleGNsdXNpdmUnLAogICAgICAgIGRlcGxveWVkQnk6IG51bGwsCiAgICAgICAgYXBwbGljYXRpb25JZDogbnVsbCwKICAgICAgICBhcHBsaWNhdGlvbk5hbWU6ICcnCiAgICAgIH0sCiAgICAgIGRlcGxveVJ1bGVzOiB7CiAgICAgICAgYXBwbGljYXRpb25JZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nlhbPogZTlupTnlKgnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwKICAgICAgICBjb250YWluZXJOYW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWuueWZqOWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICBpbWFnZUlkOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqemVnOWDjycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLAogICAgICAgIHNlcnZpY2VQb3J0OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WF<PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "index.vue", "sourceRoot": "src/views/docker/container", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <el-form ref=\"searchForm\" :model=\"listQuery\" inline @submit.native.prevent=\"getList\">\n          <div class=\"filter-inner\">\n            <el-form-item label=\"容器名称\">\n              <el-input v-model.trim=\"listQuery.containerName\" clearable placeholder=\"容器名称\" />\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"listQuery.status\" clearable placeholder=\"请选择状态\">\n                <el-option label=\"运行中\" value=\"running\" />\n                <el-option label=\"已停止\" value=\"stopped\" />\n                <el-option label=\"已暂停\" value=\"paused\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"所属应用\">\n              <el-select v-model=\"listQuery.applicationId\" clearable placeholder=\"请选择应用\" filterable>\n                <el-option\n                  v-for=\"app in appOptions\"\n                  :key=\"app.value\"\n                  :label=\"app.label\"\n                  :value=\"app.value\"\n                />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"设备类型\">\n              <el-select v-model=\"listQuery.deviceResourceType\" clearable placeholder=\"请选择设备类型\">\n                <el-option label=\"VSM\" value=\"VSM\" />\n                <el-option label=\"CHSM\" value=\"CHSM\" />\n                <el-option label=\"HSM\" value=\"HSM\" />\n              </el-select>\n            </el-form-item>\n          </div>\n          <div class=\"pull-right\">\n            <el-button-group>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\"><i class=\"el-icon-search\" /> 查询</el-button>\n                <el-button type=\"info\" @click=\"handleResetSearch\"><i class=\"el-icon-refresh\" /> 重置</el-button>\n              </el-form-item>\n            </el-button-group>\n          </div>\n        </el-form>\n      </div>\n    </el-card>\n    \n    <el-card shadow=\"never\">\n      <div class=\"filter-container clearfix\">\n        <div class=\"filter-inner\">\n          <el-button type=\"primary\" @click=\"handleDeployContainer\">部署容器</el-button>\n          <el-button type=\"success\" @click=\"handleSyncStatus\">同步状态</el-button>\n        </div>\n      </div>\n      \n      <el-table\n        ref=\"dataTable\"\n        v-loading=\"loading\"\n        :data=\"tableList\"\n        style=\"width: 100%;\"\n      >\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column align=\"center\" label=\"容器名称\" min-width=\"150\" prop=\"containerName\" />\n        <el-table-column align=\"center\" label=\"所属应用\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.applicationName || '-' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"镜像\" min-width=\"150\">\n          <template v-slot=\"{row}\">\n            <span>{{ row.imageName }}:{{ row.imageTag || 'latest' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"状态\" min-width=\"100\" prop=\"status\">\n          <template v-slot=\"{row}\">\n            <el-tag v-if=\"row.status === 'running'\" type=\"success\">运行中</el-tag>\n            <el-tag v-else-if=\"row.status === 'stopped'\" type=\"danger\">已停止</el-tag>\n            <el-tag v-else-if=\"row.status === 'paused'\" type=\"warning\">已暂停</el-tag>\n            <el-tag v-else>{{ row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"端口\" min-width=\"120\">\n          <template v-slot=\"{row}\">\n            <span>{{ formatPorts(row.portMappings) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"设备资源\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.deviceResources && row.deviceResources.length > 0\">\n              <!-- 如果只有一个设备，显示详细信息 -->\n              <div v-if=\"row.deviceResources.length === 1\" class=\"device-resource-item\">\n                <el-tag :type=\"getDeviceTypeColor(row.deviceResources[0].deviceResourceType)\" size=\"mini\">\n                  {{ row.deviceResources[0].deviceResourceType }}\n                </el-tag>\n                <div class=\"device-info-mini\">\n                  {{ row.deviceResources[0].deviceResourceName || row.deviceResources[0].deviceResourceId }}\n                </div>\n              </div>\n              <!-- 如果有多个设备，显示紧凑格式 -->\n              <div v-else class=\"device-resource-compact\">\n                <div class=\"device-tags\">\n                  <el-tag\n                    v-for=\"device in row.deviceResources\"\n                    :key=\"device.id\"\n                    :type=\"getDeviceTypeColor(device.deviceResourceType)\"\n                    size=\"mini\"\n                    :title=\"`${device.deviceResourceType}: ${device.deviceResourceName || device.deviceResourceId}`\"\n                    style=\"margin: 1px;\"\n                  >\n                    {{ device.deviceResourceType }}\n                  </el-tag>\n                </div>\n                <div class=\"device-count-info\">\n                  共{{ row.deviceResources.length }}个设备\n                </div>\n              </div>\n            </div>\n            <div v-else-if=\"row.hsmDeviceId || row.hsmConfigured\">\n              <el-tag type=\"success\" size=\"mini\">HSM</el-tag>\n              <div v-if=\"row.hsmDeviceName\" class=\"device-info-mini\">\n                {{ row.hsmDeviceName }}\n              </div>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"访问地址\" min-width=\"180\">\n          <template v-slot=\"{row}\">\n            <div v-if=\"row.accessUrl\">\n              <el-link :href=\"row.accessUrl\" target=\"_blank\" type=\"primary\">\n                {{ row.accessUrl }}\n              </el-link>\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"copyToClipboard(row.accessUrl)\"\n                style=\"margin-left: 5px;\"\n              >\n                <i class=\"el-icon-copy-document\"></i>\n              </el-button>\n            </div>\n            <el-button\n              v-else-if=\"row.status === 'running'\"\n              type=\"text\"\n              size=\"mini\"\n              @click=\"handleConfigRoute(row)\"\n            >\n              配置访问\n            </el-button>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" label=\"部署信息\" min-width=\"140\">\n          <template v-slot=\"{row}\">\n            <div class=\"deploy-info\">\n              <div v-if=\"row.deployedByName\" class=\"deploy-user\">\n                <i class=\"el-icon-user\"></i> {{ row.deployedByName }}\n              </div>\n              <div v-if=\"row.createTime\" class=\"deploy-time\">\n                <i class=\"el-icon-time\"></i> {{ formatDate(row.createTime) }}\n              </div>\n              <div v-if=\"row.replicas && row.replicas > 1\" class=\"deploy-replicas\">\n                <i class=\"el-icon-copy-document\"></i> 副本: {{ row.replicas }}\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"250\">\n          <template v-slot=\"{row}\">\n            <el-button v-if=\"row.status === 'running'\" type=\"text\" @click=\"handleStopContainer(row)\">停止</el-button>\n            <el-button v-else type=\"text\" @click=\"handleStartContainer(row)\">启动</el-button>\n            <el-button type=\"text\" @click=\"handleRestartContainer(row)\">重启</el-button>\n            <el-button type=\"text\" @click=\"handleScaleContainer(row)\">扩缩容</el-button>\n            <el-button type=\"text\" @click=\"handleViewLogs(row)\">日志</el-button>\n            <el-button v-if=\"row.accessUrl\" type=\"text\" @click=\"handleUpdateRoute(row)\">更新路由</el-button>\n            <el-button type=\"text\" @click=\"handleRemoveContainer(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"total > listQuery.pageSize\"\n        :limit.sync=\"listQuery.pageSize\"\n        :page.sync=\"listQuery.page\"\n        :total=\"total\"\n        layout=\"prev, pager, next\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n    \n    <!-- 部署容器对话框 -->\n    <el-dialog :visible.sync=\"deployDialogVisible\" title=\"部署容器\" width=\"600px\">\n      <el-form ref=\"deployForm\" :model=\"deployForm\" :rules=\"deployRules\" label-width=\"120px\">\n        <el-form-item label=\"关联应用\" prop=\"applicationId\">\n          <el-select v-model=\"deployForm.applicationId\" filterable placeholder=\"请选择应用\" style=\"width: 100%;\" :loading=\"appOptionsLoading\">\n            <el-option v-for=\"opt in appOptions\" :key=\"opt.value\" :label=\"opt.label\" :value=\"String(opt.value)\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"容器名称\" prop=\"containerName\">\n          <el-input v-model.trim=\"deployForm.containerName\" placeholder=\"例如: my-nginx\" />\n        </el-form-item>\n        <el-form-item label=\"镜像选择\" prop=\"imageId\">\n          <el-select \n            v-model=\"deployForm.imageId\" \n            filterable \n            placeholder=\"请选择镜像\"\n            @change=\"handleImageChange\"\n            style=\"width: 100%;\"\n            :loading=\"imageListLoading\"\n            :disabled=\"imageList.length === 0\"\n          >\n            <el-option\n              v-for=\"image in imageList\"\n              :key=\"image.id\"\n              :label=\"`${image.name}:${image.tag}`\"\n              :value=\"image.id\"\n            >\n              <span style=\"float: left\">{{ image.name }}:{{ image.tag }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ formatImageSize(image.sizeMb) }}</span>\n            </el-option>\n            <div v-if=\"imageList.length === 0\" slot=\"empty\">\n              <span v-if=\"imageListLoading\">加载中...</span>\n              <span v-else>暂无可用镜像，请先构建或导入镜像</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择已有的Docker镜像进行部署\n            <el-button \n              type=\"text\" \n              size=\"mini\" \n              @click=\"loadImageList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"镜像信息\" v-if=\"selectedImage\">\n          <div class=\"image-info\">\n            <p><strong>名称：</strong>{{ selectedImage.name }}</p>\n            <p><strong>标签：</strong>{{ selectedImage.tag }}</p>\n            <p><strong>大小：</strong>{{ formatImageSize(selectedImage.sizeMb) }}</p>\n            <p v-if=\"selectedImage.description\"><strong>描述：</strong>{{ selectedImage.description }}</p>\n            <p><strong>服务端口：</strong>8300（Console服务固定端口）</p>\n          </div>\n        </el-form-item>\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"deployForm.replicas\" :min=\"1\" />\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"deployForm.replicas > 1\">\n          <el-select v-model=\"deployForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n            <el-option label=\"跨可用区分散\" value=\"SPREAD_ACROSS_ZONES\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略，推荐选择跨节点分散</div>\n        </el-form-item>\n\n        <el-form-item label=\"设备资源配置\">\n          <el-switch v-model=\"deployForm.enableDeviceResource\" active-text=\"启用\" inactive-text=\"禁用\" @change=\"handleDeviceResourceToggle\" />\n          <div class=\"form-help\">启用后可配置各类设备资源（HSM、VSM、CHSM等）</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource\" label=\"设备类型\" prop=\"deviceResourceType\">\n          <el-select\n            v-model=\"deployForm.deviceResourceType\"\n            placeholder=\"请选择设备类型\"\n            @change=\"handleDeviceTypeChange\"\n            style=\"width: 100%;\"\n          >\n            <el-option label=\"VSM (虚拟密码机)\" value=\"VSM\" />\n          </el-select>\n          <div class=\"form-help\">选择要使用的设备资源类型，当前只支持VSM虚拟密码机</div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource && deployForm.deviceResourceType\" label=\"分配类型\" prop=\"allocationType\">\n          <el-radio-group v-model=\"deployForm.allocationType\">\n            <el-radio label=\"exclusive\">独占模式</el-radio>\n            <el-radio label=\"shared\">共享模式</el-radio>\n          </el-radio-group>\n          <div class=\"form-help\">\n            独占模式：设备只能被当前容器使用；共享模式：设备可被多个容器共享使用\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource && deployForm.deviceResourceType\" label=\"选择设备\" prop=\"deviceResourceIds\">\n          <el-select\n            v-model=\"deployForm.deviceResourceIds\"\n            multiple\n            filterable\n            :placeholder=\"`请选择${deployForm.deviceResourceType}设备`\"\n            @change=\"handleDeviceResourceChange\"\n            style=\"width: 100%;\"\n            :loading=\"deviceResourceListLoading\"\n            :disabled=\"availableDeviceResources.length === 0\"\n          >\n            <el-option\n              v-for=\"device in availableDeviceResources\"\n              :key=\"device.id\"\n              :label=\"device.name\"\n              :value=\"device.id\"\n              :disabled=\"!device.available\"\n            >\n              <div class=\"device-option\">\n                <div class=\"device-option-main\">\n                  <span class=\"device-name\">{{ device.name }}</span>\n                  <el-tag\n                    :type=\"device.available ? 'success' : 'danger'\"\n                    size=\"mini\"\n                    class=\"device-status\"\n                  >\n                    {{ device.available ? '可用' : '不可用' }}\n                  </el-tag>\n                </div>\n                <div class=\"device-option-detail\">\n                  <span class=\"device-address\">{{ device.ipAddress }}:{{ device.port }}</span>\n                  <span class=\"device-capacity\" v-if=\"device.totalCapacity\">\n                    容量: {{ device.usedCapacity || 0 }}/{{ device.totalCapacity }}\n                  </span>\n                </div>\n              </div>\n            </el-option>\n            <div v-if=\"availableDeviceResources.length === 0\" slot=\"empty\">\n              <span v-if=\"deviceResourceListLoading\">加载中...</span>\n              <span v-else>暂无可用的{{ deployForm.deviceResourceType }}设备</span>\n            </div>\n          </el-select>\n          <div class=\"form-help\">\n            选择可用的{{ deployForm.deviceResourceType }}设备资源\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              @click=\"loadDeviceResourceList\"\n              style=\"margin-left: 8px;\"\n            >\n              <i class=\"el-icon-refresh\"></i> 刷新\n            </el-button>\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"deployForm.enableDeviceResource && selectedDeviceResources.length > 0\" label=\"设备信息\">\n          <div class=\"selected-devices\">\n            <div v-for=\"device in selectedDeviceResources\" :key=\"device.id\" class=\"device-info-card\">\n              <div class=\"device-card-header\">\n                <span class=\"device-card-name\">{{ device.name }}</span>\n                <el-tag :type=\"getDeviceTypeColor(device.type)\" size=\"mini\">{{ device.type }}</el-tag>\n              </div>\n              <div class=\"device-card-content\">\n                <p><strong>地址：</strong>{{ device.ipAddress }}:{{ device.port }}</p>\n                <p v-if=\"device.managementPort\"><strong>管理端口：</strong>{{ device.managementPort }}</p>\n                <p><strong>状态：</strong>\n                  <el-tag v-if=\"device.status === 'normal'\" type=\"success\" size=\"mini\">正常</el-tag>\n                  <el-tag v-else-if=\"device.status === 'running'\" type=\"success\" size=\"mini\">运行中</el-tag>\n                  <el-tag v-else type=\"warning\" size=\"mini\">{{ device.status }}</el-tag>\n                </p>\n                <p v-if=\"device.totalCapacity\"><strong>容量：</strong>{{ device.usedCapacity || 0 }}/{{ device.totalCapacity }}</p>\n                <p v-if=\"device.description\"><strong>描述：</strong>{{ device.description }}</p>\n              </div>\n            </div>\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"deployDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmDeployContainer\">部署</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 容器日志对话框 -->\n    <el-dialog :visible.sync=\"logsDialogVisible\" title=\"容器日志\" width=\"800px\">\n      <el-input\n        v-model=\"containerLogs\"\n        :rows=\"20\"\n        readonly\n        type=\"textarea\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"logsDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 扩缩容对话框 -->\n    <el-dialog :visible.sync=\"scaleDialogVisible\" title=\"容器扩缩容\" width=\"500px\">\n      <el-form ref=\"scaleForm\" :model=\"scaleForm\" :rules=\"scaleRules\" label-width=\"100px\">\n        <el-form-item label=\"副本数\" prop=\"replicas\">\n          <el-input-number v-model=\"scaleForm.replicas\" :min=\"1\" />\n          <div class=\"form-help\">当前运行的容器实例数量</div>\n        </el-form-item>\n        <el-form-item label=\"分布策略\" v-if=\"scaleForm.replicas > 1\">\n          <el-select v-model=\"scaleForm.distributionStrategy\" placeholder=\"请选择分布策略\" style=\"width: 100%;\">\n            <el-option label=\"跨节点分散（推荐）\" value=\"SPREAD_ACROSS_NODES\" />\n            <el-option label=\"仅Worker节点\" value=\"WORKER_NODES_ONLY\" />\n            <el-option label=\"仅Manager节点\" value=\"MANAGER_NODES_ONLY\" />\n            <el-option label=\"平衡分布\" value=\"BALANCED\" />\n          </el-select>\n          <div class=\"form-help\">多副本时的部署分布策略</div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"scaleDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmScaleContainer\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Pagination from '@/components/Pagination'\nimport {\n  getContainerList,\n  deployContainer,\n  getRecommendedStrategy,\n  startContainer,\n  stopContainer,\n  restartContainer,\n  removeContainer,\n  getContainerLogs,\n  syncContainerStatus,\n  scaleContainer,\n  updateContainerImage,\n  configContainerRoute,\n  updateContainerRoute,\n  getAvailableDeviceResources,\n  getContainerDeviceResources,\n  getContainerAccessUrl\n} from '@/api/docker/container'\nimport { getImageList } from '@/api/docker/image'\nimport { fetchApplicationOptions, fetchUserApps } from '@/api/application'\nimport { getAvailableHsmDevices, getHsmDeviceDetail } from '@/api/docker/hsm'\n\nexport default {\n  name: 'DockerContainerIndex',\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      tableList: [],\n      loading: false,\n      total: 0,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined,\n        applicationId: undefined,\n        deviceResourceType: undefined\n      },\n      logsDialogVisible: false,\n      containerLogs: '',\n      currentContainer: null,\n      deployDialogVisible: false,\n      deployForm: {\n        containerName: '',\n        imageId: null,\n        imageName: '',\n        imageTag: 'latest',\n        servicePort: 80,\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES',\n        enableHsm: false,\n        hsmDeviceId: null,\n        // 新的设备资源配置\n        enableDeviceResource: false,\n        deviceResourceType: '',\n        deviceResourceIds: [],\n        allocationType: 'exclusive',\n        deployedBy: null,\n        applicationId: null,\n        applicationName: ''\n      },\n      deployRules: {\n        applicationId: [{ required: true, message: '请选择关联应用', trigger: 'change' }],\n        containerName: [{ required: true, message: '请输入容器名称', trigger: 'blur' }],\n        imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],\n        servicePort: [{ required: true, message: '请输入服务端口', trigger: 'blur' }]\n      },\n      scaleDialogVisible: false,\n      scaleForm: {\n        id: '',\n        replicas: 1,\n        distributionStrategy: 'SPREAD_ACROSS_NODES'\n      },\n      scaleRules: {\n        replicas: [{ required: true, message: '请输入副本数量', trigger: 'blur' }]\n      },\n      // 镜像相关数据\n      imageList: [],\n      imageListLoading: false,\n      selectedImage: null,\n      // HSM设备相关数据\n      hsmDeviceList: [],\n      hsmDeviceListLoading: false,\n      selectedHsmDevice: null,\n      // 设备资源相关数据\n      availableDeviceResources: [],\n      deviceResourceListLoading: false,\n      selectedDeviceResources: [],\n      // 应用下拉\n      appOptions: [],\n      appOptionsLoading: false\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.getList()\n    },\n    handleResetSearch() {\n      this.listQuery = {\n        page: 1,\n        pageSize: 20,\n        containerName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    handleDeployContainer() {\n      // 首先加载镜像列表\n      Promise.all([this.loadImageList(), this.loadAppOptions()]).then(() => {\n        this.deployForm = {\n          containerName: '',\n          imageId: null,\n          imageName: '',\n          imageTag: 'latest',\n          replicas: 1,\n          distributionStrategy: 'SPREAD_ACROSS_NODES',\n          enableHsm: false,\n          hsmDeviceId: null,\n          // 重置新的设备资源配置\n          enableDeviceResource: false,\n          deviceResourceType: '',\n          deviceResourceIds: [],\n          allocationType: 'exclusive',\n          deployedBy: this.user ? this.user.id : null,\n          applicationId: null,\n          applicationName: ''\n        }\n        this.selectedImage = null\n        this.selectedHsmDevice = null\n        this.selectedDeviceResources = []\n        this.availableDeviceResources = []\n        this.deployDialogVisible = true\n        this.$nextTick(() => {\n          this.$refs['deployForm'].clearValidate()\n        })\n      })\n    },\n    confirmDeployContainer() {\n      this.$refs['deployForm'].validate((valid) => {\n        if (valid) {\n          // 检查HSM设备配置\n          if (this.deployForm.enableHsm && !this.deployForm.hsmDeviceId) {\n            this.$message.error('请选择HSM设备')\n            return\n          }\n\n          // 检查设备资源配置\n          if (this.deployForm.enableDeviceResource) {\n            if (!this.deployForm.deviceResourceType) {\n              this.$message.error('请选择设备资源类型')\n              return\n            }\n            if (!this.deployForm.deviceResourceIds || this.deployForm.deviceResourceIds.length === 0) {\n              this.$message.error('请选择设备资源')\n              return\n            }\n          }\n          \n          const deployData = { ...this.deployForm }\n          \n          // 确保镜像信息完整\n          if (this.selectedImage) {\n            deployData.imageName = this.selectedImage.imageName\n            deployData.imageTag = this.selectedImage.imageTag\n          }\n          \n          // 构建HSM设备配置\n          if (this.deployForm.enableHsm && this.selectedHsmDevice) {\n            deployData.hsmDeviceConfig = {\n              encryptorGroupId: 1, // 默认组ID\n              encryptorId: this.selectedHsmDevice.deviceId,\n              encryptorName: this.selectedHsmDevice.deviceName,\n              serverIpAddr: this.selectedHsmDevice.ipAddress,\n              serverPort: this.selectedHsmDevice.managementPort, // 使用管理端口8018\n              tcpConnNum: this.selectedHsmDevice.tcpConnNum || 5,\n              msgHeadLen: this.selectedHsmDevice.msgHeadLen || 4,\n              msgTailLen: this.selectedHsmDevice.msgTailLen || 0,\n              asciiOrEbcdic: this.selectedHsmDevice.encoding || 0,\n              dynamicLibPath: './libdeviceapi.so'\n            }\n          }\n          \n          // 不再使用Traefik配置，使用Docker Swarm内置负载均衡\n\n          // 设置分布策略\n          if (deployData.replicas > 1 && deployData.distributionStrategy) {\n            deployData.distributionStrategy = deployData.distributionStrategy\n          }\n\n          // 新的设备资源配置（优先使用新的配置方式）\n          if (this.deployForm.enableDeviceResource && this.deployForm.deviceResourceIds.length > 0) {\n            deployData.deviceResourceConfig = {\n              deviceResourceType: this.deployForm.deviceResourceType,\n              deviceResourceIds: this.deployForm.deviceResourceIds,\n              allocationType: this.deployForm.allocationType,\n              priority: 1,\n              configData: JSON.stringify({\n                deviceType: this.deployForm.deviceResourceType,\n                allocationType: this.deployForm.allocationType,\n                selectedDevices: this.selectedDeviceResources.map(device => ({\n                  id: device.id,\n                  name: device.name,\n                  type: device.type,\n                  ipAddress: device.ipAddress,\n                  port: device.port\n                }))\n              })\n            }\n          }\n          // 兼容旧的HSM设备配置\n          else if (this.deployForm.enableHsm && this.selectedHsmDevice && deployData.hsmDeviceConfig) {\n            deployData.deviceResourceConfig = {\n              deviceResourceType: 'VSM',\n              deviceResourceIds: [this.selectedHsmDevice.deviceId],\n              allocationType: 'exclusive',\n              priority: 1,\n              configData: JSON.stringify(deployData.hsmDeviceConfig)\n            }\n          }\n\n          // 使用统一的部署接口\n          console.log('使用统一的部署接口，配置：', {\n            hsm: this.deployForm.enableHsm,\n            strategy: deployData.distributionStrategy,\n            replicas: deployData.replicas\n          })\n\n          deployContainer(deployData).then((response) => {\n            this.deployDialogVisible = false\n            if (response.code === 20000) {\n              if (response.data) {\n                // 处理不同的响应结构\n                let message = '容器部署成功！'\n                \n                if (response.data.accessUrl) {\n                  message += `访问地址：${response.data.accessUrl}`\n                } else if (response.data.container && response.data.container.accessUrl) {\n                  message += `访问地址：${response.data.container.accessUrl}`\n                }\n                \n                if (response.data.distributionStrategy) {\n                  message += `，分布策略：${this.getStrategyDisplayName(response.data.distributionStrategy)}`\n                }\n                \n                if (response.data.hsmConfigured) {\n                  message += `，HSM设备：${this.selectedHsmDevice.deviceName}`\n                }\n                \n                this.$message.success(message)\n              } else {\n                this.$message.success('容器部署任务已提交')\n              }\n              this.getList()\n            } else {\n              this.$message.error(response.message || '部署失败')\n            }\n          }).catch(() => {\n            // 错误处理已在拦截器中处理\n          })\n        }\n      })\n    },\n\n    // 加载应用选项\n    async loadAppOptions() {\n      this.appOptionsLoading = true\n      try {\n        let resp\n        // 租户角色使用 user/list-options，其它使用 fetch-options\n        if (this.user && Array.isArray(this.user.roles) && this.user.roles.includes('ROLE_TENANT')) {\n          resp = await fetchUserApps()\n        } else {\n          resp = await fetchApplicationOptions()\n        }\n        if (resp && resp.code === 20000) {\n          const data = Array.isArray(resp.data) ? resp.data : (resp.data && resp.data.list) ? resp.data.list : []\n          this.appOptions = data.map(item => {\n            if (item.label && (item.value !== undefined)) return item\n            return { label: item.name || item.applicationName || item.label, value: String(item.id || item.value) }\n          })\n        } else {\n          this.appOptions = []\n        }\n      } catch (e) {\n        this.appOptions = []\n      } finally {\n        this.appOptionsLoading = false\n      }\n    },\n    handleStartContainer(row) {\n      startContainer(row.id).then(() => {\n        this.$message.success('容器启动成功')\n        this.getList()\n      })\n    },\n    handleStopContainer(row) {\n      stopContainer(row.id).then(() => {\n        this.$message.success('容器停止成功')\n        this.getList()\n      })\n    },\n    handleRestartContainer(row) {\n      restartContainer(row.id).then(() => {\n        this.$message.success('容器重启成功')\n        this.getList()\n      })\n    },\n    handleRemoveContainer(row) {\n      this.$msgbox.confirm(`确认要删除容器 ${row.containerName} 吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeContainer(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      }).catch(() => {\n        // 取消删除\n      })\n    },\n    handleScaleContainer(row) {\n      this.scaleForm = {\n        id: row.id,\n        replicas: row.replicas || 1, // 使用当前副本数或默认值\n        distributionStrategy: row.distributionStrategy || 'SPREAD_ACROSS_NODES'\n      }\n      this.scaleDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs['scaleForm'].clearValidate()\n      })\n    },\n    confirmScaleContainer() {\n      this.$refs['scaleForm'].validate((valid) => {\n        if (valid) {\n          scaleContainer(this.scaleForm.id, this.scaleForm.replicas).then(() => {\n            this.scaleDialogVisible = false\n            this.$message.success('容器扩缩容任务已提交')\n            this.getList()\n          })\n        }\n      })\n    },\n    handleViewLogs(row) {\n      this.currentContainer = row\n      getContainerLogs(row.id).then(response => {\n        if (response.code === 20000) {\n          this.containerLogs = response.data || ''\n          this.logsDialogVisible = true\n        }\n      })\n    },\n    handleSyncStatus() {\n      syncContainerStatus().then(() => {\n        this.$message.success('容器状态同步任务已提交')\n        this.getList()\n      })\n    },\n    getList() {\n      this.loading = true\n      getContainerList(this.listQuery).then(response => {\n        if (response.code === 20000) {\n          this.tableList = response.data.list || []\n          this.total = response.data.totalCount || 0\n\n          // 为每个容器加载设备资源信息\n          this.loadContainerDeviceResources()\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n\n    // 加载容器设备资源信息\n    async loadContainerDeviceResources() {\n      if (!this.tableList || this.tableList.length === 0) {\n        return\n      }\n\n      // 并发加载所有容器的设备资源信息\n      const promises = this.tableList.map(async (container) => {\n        try {\n          const response = await getContainerDeviceResources(container.id)\n          if (response.code === 20000 && response.data) {\n            // 处理不同的数据结构\n            let deviceResources = []\n            if (Array.isArray(response.data)) {\n              // 直接数组结构\n              deviceResources = response.data\n            } else if (response.data.list && Array.isArray(response.data.list)) {\n              // 分页结构：{totalCount: 1, list: [...]}\n              deviceResources = response.data.list\n            }\n            // 将设备资源信息添加到容器对象中\n            this.$set(container, 'deviceResources', deviceResources)\n          }\n        } catch (error) {\n          console.warn(`获取容器${container.id}的设备资源信息失败:`, error)\n        }\n      })\n\n      await Promise.all(promises)\n    },\n    // 格式化端口映射显示\n    formatPorts(portMappings) {\n      if (!portMappings) return '-'\n      try {\n        const ports = JSON.parse(portMappings)\n        if (Array.isArray(ports) && ports.length > 0) {\n          // return ports.map(p => `${p.hostPort || ''}:${p.containerPort}/${p.protocol || 'tcp'}`).join(', ')\n          return ports.map(p => `${p.hostPort || ''}:/${p.protocol || 'tcp'}`).join(', ')\n        }\n        return '-'\n      } catch (e) {\n        return portMappings\n      }\n    },\n    // 格式化日期显示\n    formatDate(dateTime) {\n      if (!dateTime) return '-'\n      return new Date(dateTime).toLocaleString('zh-CN')\n    },\n    \n    // 复制到剪贴板\n    copyToClipboard(text) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(text).then(() => {\n          this.$message.success('地址已复制到剪贴板')\n        })\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea')\n        textArea.value = text\n        document.body.appendChild(textArea)\n        textArea.select()\n        document.execCommand('copy')\n        document.body.removeChild(textArea)\n        this.$message.success('地址已复制到剪贴板')\n      }\n    },\n    \n    // 配置访问路由\n    async handleConfigRoute(row) {\n      try {\n        // 先获取容器的访问地址信息\n        const response = await getContainerAccessUrl(row.id)\n        if (response.code === 20000 && response.data) {\n          const { accessPort, accessInfo } = response.data\n\n          if (accessPort && accessPort > 0) {\n            // 如果已有分配的端口，显示访问信息\n            this.$alert(\n              `${accessInfo}\\n\\n当前使用Docker Swarm内置负载均衡，无需额外配置路由。`,\n              '容器访问信息',\n              {\n                confirmButtonText: '确定',\n                type: 'info'\n              }\n            )\n          } else {\n            // 如果没有分配端口，提示用户\n            this.$alert(\n              '该容器尚未分配外部访问端口。\\n\\n请确保容器部署时配置了端口映射，或联系管理员分配端口。',\n              '无访问端口',\n              {\n                confirmButtonText: '确定',\n                type: 'warning'\n              }\n            )\n          }\n        } else {\n          this.$message.error('获取容器访问信息失败：' + (response.message || '未知错误'))\n        }\n      } catch (error) {\n        console.error('获取容器访问信息失败:', error)\n        this.$message.error('获取容器访问信息失败')\n      }\n    },\n    \n    // 更新路由配置\n    handleUpdateRoute(row) {\n      this.$prompt('请输入新的服务端口', '更新路由', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputValidator: (value) => {\n          const port = parseInt(value)\n          if (!port || port < 1 || port > 65535) {\n            return '请输入有效的端口号(1-65535)'\n          }\n          return true\n        }\n      }).then(({ value }) => {\n        const routeConfig = {\n          containerId: row.id,\n          servicePort: parseInt(value)\n        }\n        \n        this.updateContainerRoute(routeConfig).then((response) => {\n          if (response.code === 20000 && response.data && response.data.accessUrl) {\n            this.$message.success(`路由更新成功！新地址：${response.data.accessUrl}`)\n            this.getList()\n          } else {\n            this.$message.error(response.message || '更新失败')\n          }\n        }).catch(() => {\n          // 错误处理已在拦截器中处理\n        })\n      }).catch(() => {\n        // 取消操作\n      })\n    },\n    \n\n    \n    // 配置容器路由方法\n    async configContainerRoute(routeConfig) {\n      return configContainerRoute(routeConfig)\n    },\n    \n    // 更新容器路由方法\n    async updateContainerRoute(routeConfig) {\n      return updateContainerRoute(routeConfig)\n    },\n    \n    // 加载镜像列表\n    async loadImageList() {\n      this.imageListLoading = true\n      try {\n        const response = await getImageList()\n        if (response.code === 20000) {\n          // 处理分页数据结构\n          if (response.data && response.data.list) {\n            // 如果返回的是分页结构\n            this.imageList = response.data.list.map(image => {\n              return {\n                ...image,\n                // 确保 sizeMb 是数字类型\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else if (Array.isArray(response.data)) {\n            // 如果返回的是直接数组\n            this.imageList = response.data.map(image => {\n              return {\n                ...image,\n                sizeMb: typeof image.sizeMb === 'string' ? parseInt(image.sizeMb) : image.sizeMb\n              }\n            })\n          } else {\n            this.imageList = []\n          }\n          \n          if (this.imageList.length === 0) {\n            this.$message.info('当前没有可用的镜像，请先构建或导入镜像')\n          }\n        } else {\n          this.$message.error('获取镜像列表失败：' + (response.message || '未知错误'))\n          this.imageList = []\n        }\n      } catch (error) {\n        console.error('加载镜像列表失败:', error)\n        this.$message.error('加载镜像列表失败')\n        this.imageList = []\n      } finally {\n        this.imageListLoading = false\n      }\n    },\n    \n    // 处理镜像选择变化\n    handleImageChange(imageId) {\n      if (imageId) {\n        this.selectedImage = this.imageList.find(img => img.id === imageId)\n        if (this.selectedImage) {\n          // 自动填充镜像名称和标签\n          this.deployForm.imageName = this.selectedImage.name\n          this.deployForm.imageTag = this.selectedImage.tag\n          \n          // 智能生成容器名称（如果当前为空）\n          if (!this.deployForm.containerName) {\n            this.suggestContainerName(this.selectedImage.name, this.selectedImage.tag)\n          }\n          \n          // 根据镜像类型智能推荐端口\n          this.suggestServicePort(this.selectedImage.name)\n        }\n      } else {\n        this.selectedImage = null\n        this.deployForm.imageName = ''\n        this.deployForm.imageTag = 'latest'\n      }\n    },\n    \n    // 智能生成容器名称\n    suggestContainerName(imageName, imageTag) {\n      // 移除镜像名称中的特殊字符，生成简洁的名称\n      let baseName = imageName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()\n      \n      // 如果标签不是latest，则加入标签\n      if (imageTag && imageTag !== 'latest') {\n        baseName += '-' + imageTag.replace(/[^a-zA-Z0-9]/g, '-')\n      }\n      \n      // 添加时间戳保证唯一性\n      const timestamp = Date.now().toString().slice(-6)\n      this.deployForm.containerName = `${baseName}-${timestamp}`\n    },\n    \n    // 根据镜像类型智能推荐端口\n    suggestServicePort(imageName) {\n      const portMap = {\n        'nginx': 80,\n        'apache': 80,\n        'httpd': 80,\n        'mysql': 3306,\n        'mariadb': 3306,\n        'postgres': 5432,\n        'postgresql': 5432,\n        'redis': 6379,\n        'mongodb': 27017,\n        'mongo': 27017,\n        'tomcat': 8080,\n        'node': 3000,\n        'spring': 8080,\n        'java': 8080\n      }\n      \n      // 查找匹配的镜像类型\n      for (const [key, port] of Object.entries(portMap)) {\n        if (imageName.toLowerCase().includes(key)) {\n          this.deployForm.servicePort = port\n          break\n        }\n      }\n    },\n    \n    // 格式化镜像大小\n    formatImageSize(sizeMb) {\n      if (!sizeMb || sizeMb === 0) return '-'\n\n      // 确保是数字类型\n      const size = typeof sizeMb === 'string' ? parseInt(sizeMb) : sizeMb\n      if (isNaN(size)) return '-'\n\n      // 如果已经是MB单位，直接显示\n      if (size < 1024) {\n        return `${size} MB`\n      }\n\n      // 转换为GB\n      const sizeGb = size / 1024\n      return `${sizeGb.toFixed(1)} GB`\n    },\n    \n    // 获取分布策略显示名称\n    getStrategyDisplayName(strategy) {\n      const strategyMap = {\n        'SPREAD_ACROSS_NODES': '跨节点分散',\n        'WORKER_NODES_ONLY': '仅Worker节点',\n        'MANAGER_NODES_ONLY': '仅Manager节点',\n        'BALANCED': '平衡分布',\n        'SPREAD_ACROSS_ZONES': '跨可用区分散'\n      }\n      return strategyMap[strategy] || strategy\n    },\n    \n    // 获取推荐的分布策略\n    async getRecommendedDistributionStrategy(replicas) {\n      try {\n        const response = await getRecommendedStrategy(replicas)\n        if (response.code === 20000 && response.data) {\n          return response.data.recommendedStrategy\n        }\n      } catch (error) {\n        console.warn('获取推荐分布策略失败:', error)\n      }\n      return 'SPREAD_ACROSS_NODES' // 默认值\n    },\n\n    // === 设备资源相关方法 ===\n\n    // 处理设备资源开关变化\n    handleDeviceResourceToggle(enabled) {\n      if (enabled) {\n        // 启用设备资源时自动选择VSM并加载设备列表\n        this.deployForm.deviceResourceType = 'VSM'\n        this.deployForm.deviceResourceIds = []\n        this.deployForm.allocationType = 'exclusive'\n        this.selectedDeviceResources = []\n        this.availableDeviceResources = []\n        // 自动加载VSM设备列表\n        this.loadDeviceResourceList()\n      } else {\n        // 禁用设备资源时清空所有相关配置\n        this.deployForm.deviceResourceType = ''\n        this.deployForm.deviceResourceIds = []\n        this.selectedDeviceResources = []\n        this.availableDeviceResources = []\n      }\n    },\n\n    // 处理设备类型变化\n    handleDeviceTypeChange(deviceType) {\n      if (deviceType) {\n        // 切换设备类型时清空已选择的设备\n        this.deployForm.deviceResourceIds = []\n        this.selectedDeviceResources = []\n        // 加载对应类型的设备列表\n        this.loadDeviceResourceList()\n      } else {\n        this.availableDeviceResources = []\n        this.selectedDeviceResources = []\n      }\n    },\n\n    // 加载设备资源列表\n    async loadDeviceResourceList() {\n      if (!this.deployForm.deviceResourceType) {\n        return\n      }\n\n      this.deviceResourceListLoading = true\n      try {\n        const response = await getAvailableDeviceResources({\n          deviceResourceType: this.deployForm.deviceResourceType,\n          excludeContainerId: null, // 新部署时不需要排除\n          allocationType: this.deployForm.allocationType // 传递用户选择的分配类型\n        })\n\n        console.log('设备资源API返回:', response) // 调试日志\n\n        if (response.code === 20000) {\n          // 处理不同的数据结构\n          let deviceList = []\n          if (response.data && Array.isArray(response.data.list)) {\n            // 分页结构：{totalCount: 12, list: [...]}\n            deviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            // 直接数组结构\n            deviceList = response.data\n          } else {\n            deviceList = []\n          }\n\n          this.availableDeviceResources = deviceList\n\n          if (this.availableDeviceResources.length === 0) {\n            this.$message.info(`当前没有可用的${this.deployForm.deviceResourceType}设备`)\n          } else {\n            console.log('加载到设备资源:', this.availableDeviceResources.length, '个')\n          }\n        } else {\n          this.$message.error('获取设备资源列表失败：' + (response.message || '未知错误'))\n          this.availableDeviceResources = []\n        }\n      } catch (error) {\n        console.error('加载设备资源列表失败:', error)\n        this.$message.error('加载设备资源列表失败')\n        this.availableDeviceResources = []\n      } finally {\n        this.deviceResourceListLoading = false\n      }\n    },\n\n    // 处理设备资源选择变化\n    handleDeviceResourceChange(deviceIds) {\n      this.selectedDeviceResources = []\n      if (deviceIds && deviceIds.length > 0) {\n        deviceIds.forEach(deviceId => {\n          const device = this.availableDeviceResources.find(d => d.id === deviceId)\n          if (device) {\n            this.selectedDeviceResources.push(device)\n          }\n        })\n      }\n    },\n\n    // 获取设备类型颜色\n    getDeviceTypeColor(deviceType) {\n      const colorMap = {\n        'VSM': 'success',\n        'CHSM': 'primary',\n        'HSM': 'warning'\n      }\n      return colorMap[deviceType] || 'info'\n    },\n\n    // === HSM设备相关方法 ===\n    \n    // 处理HSM开关变化\n    handleHsmToggle(enabled) {\n      if (enabled) {\n        // 启用HSM时加载设备列表\n        this.loadHsmDeviceList()\n      } else {\n        // 禁用HSM时清空选择\n        this.deployForm.hsmDeviceId = null\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备列表\n    async loadHsmDeviceList() {\n      this.hsmDeviceListLoading = true\n      try {\n        const response = await getAvailableHsmDevices({\n          status: 'running'\n        })\n        \n        if (response.code === 20000) {\n          if (response.data && response.data.list) {\n            this.hsmDeviceList = response.data.list\n          } else if (Array.isArray(response.data)) {\n            this.hsmDeviceList = response.data\n          } else {\n            this.hsmDeviceList = []\n          }\n          \n          if (this.hsmDeviceList.length === 0) {\n            this.$message.info('当前没有可用的HSM设备')\n          }\n        } else {\n          this.$message.error('获取HSM设备列表失败：' + (response.message || '未知错误'))\n          this.hsmDeviceList = []\n        }\n      } catch (error) {\n        console.error('加载HSM设备列表失败:', error)\n        this.$message.error('加载HSM设备列表失败')\n        this.hsmDeviceList = []\n      } finally {\n        this.hsmDeviceListLoading = false\n      }\n    },\n    \n    // 处理HSM设备选择变化\n    handleHsmDeviceChange(deviceId) {\n      if (deviceId) {\n        this.selectedHsmDevice = this.hsmDeviceList.find(device => device.deviceId === deviceId)\n        if (this.selectedHsmDevice) {\n          // 可以在这里加载更详细的设备信息\n          this.loadHsmDeviceDetail(deviceId)\n        }\n      } else {\n        this.selectedHsmDevice = null\n      }\n    },\n    \n    // 加载HSM设备详细信息\n    async loadHsmDeviceDetail(deviceId) {\n      try {\n        const response = await getHsmDeviceDetail(deviceId)\n        if (response.code === 20000 && response.data) {\n          this.selectedHsmDevice = response.data\n        }\n      } catch (error) {\n        console.warn('获取HSM设备详细信息失败:', error)\n      }\n    },\n    \n\n  },\n  mounted() {\n    this.getList()\n    // 预加载镜像列表，提升用户体验\n    this.loadImageList()\n    // 预加载HSM设备列表\n    this.loadHsmDeviceList()\n    // 预加载应用选项\n    this.loadAppOptions()\n  },\n  computed: {\n    ...mapGetters([\n      'user'\n    ])\n  },\n  watch: {\n    // 监听副本数变化，自动更新推荐的分布策略\n    'deployForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.deployForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        // 单副本时不需要分布策略\n        this.deployForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听扩缩容副本数变化\n    'scaleForm.replicas'(newReplicas, oldReplicas) {\n      if (newReplicas > 1 && newReplicas !== oldReplicas) {\n        this.getRecommendedDistributionStrategy(newReplicas).then(strategy => {\n          this.scaleForm.distributionStrategy = strategy\n        })\n      } else if (newReplicas === 1) {\n        this.scaleForm.distributionStrategy = 'BALANCED'\n      }\n    },\n    // 监听分配类型变化，重新加载设备列表\n    'deployForm.allocationType'(newAllocationType, oldAllocationType) {\n      if (newAllocationType !== oldAllocationType && this.deployForm.deviceResourceType) {\n        // 清空已选择的设备\n        this.deployForm.deviceResourceIds = []\n        this.selectedDeviceResources = []\n        // 重新加载设备列表\n        this.loadDeviceResourceList()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.filter-inner {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .el-form-item {\n    margin-bottom: 10px;\n    margin-right: 15px;\n  }\n}\n\n.pull-right {\n  float: right;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.form-help {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.el-link {\n  font-size: 12px;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n.image-info {\n  background-color: #f5f7fa;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.device-info {\n  background-color: #f0f9ff;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #a7f3d0;\n  \n  p {\n    margin: 4px 0;\n    font-size: 13px;\n    \n    strong {\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.strategy-info {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.hsm-info {\n  font-size: 11px;\n  color: #67c23a;\n  margin-top: 2px;\n}\n\n// 设备资源相关样式\n.device-resource-item {\n  margin-bottom: 4px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.device-info-mini {\n  font-size: 11px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.device-option {\n  width: 100%;\n\n  .device-option-main {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 2px;\n\n    .device-name {\n      font-weight: 500;\n    }\n\n    .device-status {\n      margin-left: 8px;\n    }\n  }\n\n  .device-option-detail {\n    display: flex;\n    justify-content: space-between;\n    font-size: 12px;\n    color: #8492a6;\n\n    .device-address {\n      flex: 1;\n    }\n\n    .device-capacity {\n      margin-left: 8px;\n    }\n  }\n}\n\n.selected-devices {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.device-info-card {\n  background-color: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 8px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  .device-card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 8px;\n\n    .device-card-name {\n      font-weight: 600;\n      color: #303133;\n    }\n  }\n\n  .device-card-content {\n    p {\n      margin: 4px 0;\n      font-size: 13px;\n      color: #606266;\n\n      strong {\n        color: #303133;\n      }\n    }\n  }\n}\n\n// 规格信息样式\n.spec-info {\n  font-size: 12px;\n\n  .resource-limit {\n    color: #909399;\n    margin-top: 2px;\n\n    span {\n      margin-right: 8px;\n    }\n  }\n}\n\n// 设备资源显示样式\n.device-resource-compact {\n  .device-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 2px;\n    margin-bottom: 2px;\n  }\n\n  .device-count-info {\n    font-size: 11px;\n    color: #909399;\n    text-align: center;\n  }\n}\n\n// 部署信息样式\n.deploy-info {\n  font-size: 12px;\n\n  .deploy-user,\n  .deploy-time,\n  .deploy-node {\n    display: flex;\n    align-items: center;\n    margin-bottom: 2px;\n    color: #606266;\n\n    i {\n      margin-right: 4px;\n      width: 12px;\n    }\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n</style>"]}]}